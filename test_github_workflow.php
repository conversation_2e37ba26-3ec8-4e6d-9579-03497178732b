<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Services\GitHubService;
use App\Models\GitHubAccount;

echo "测试GitHub工作流创建流程...\n\n";

try {
    // 创建GitHubService实例
    $githubService = new GitHubService();
    
    // 获取最佳GitHub账号
    echo "1. 获取最佳GitHub账号...\n";
    $bestAccount = $githubService->getBestAccount();
    
    if (!$bestAccount) {
        echo "❌ 没有可用的GitHub账号\n";
        exit(1);
    }
    
    echo "✅ 找到GitHub账号: {$bestAccount['username']}\n";
    echo "Token前缀: " . substr($bestAccount['token'], 0, 10) . "...\n\n";
    
    // 测试Token有效性
    echo "2. 测试Token有效性...\n";
    $tokenTest = $githubService->testToken($bestAccount['token']);
    
    if (!$tokenTest['valid']) {
        echo "❌ Token无效: {$tokenTest['error']}\n";
        exit(1);
    }
    
    echo "✅ Token有效\n";
    echo "剩余API调用次数: {$tokenTest['remaining']}\n\n";
    
    // 准备测试配置
    $testConfig = [
        'upload_id' => 'test_' . time(),
        'auth_method' => 'api_key',
        'auth_data' => [
            'api_key_id' => 'TEST_API_KEY_ID',
            'issuer_id' => 'TEST_ISSUER_ID',
            'api_key_content' => 'TEST_API_KEY_CONTENT'
        ],
        'download_url' => 'https://example.com/test.ipa',
        'bundle_id' => 'com.example.testapp',
        'app_name' => 'Test App',
        'version' => '1.0.0',
        'build' => '1'
    ];
    
    echo "3. 测试配置:\n";
    echo json_encode($testConfig, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
    
    // 创建上传工作流
    echo "4. 创建上传工作流...\n";
    echo "==========================================\n";
    
    $result = $githubService->createUploadWorkflow($bestAccount, $testConfig);
    
    echo "==========================================\n";
    echo "✅ 工作流创建完成!\n\n";
    
    echo "5. 结果信息:\n";
    echo "仓库名称: {$result['repo_name']}\n";
    echo "工作流ID: {$result['workflow_id']}\n";
    echo "GitHub URL: {$result['github_url']}\n\n";
    
    echo "6. 验证步骤:\n";
    echo "- 访问GitHub仓库: https://github.com/{$bestAccount['username']}/{$result['repo_name']}\n";
    echo "- 检查工作流文件: https://github.com/{$bestAccount['username']}/{$result['repo_name']}/blob/main/.github/workflows/upload-testflight.yml\n";
    echo "- 查看Actions页面: {$result['github_url']}\n";
    echo "- 检查仓库密钥: https://github.com/{$bestAccount['username']}/{$result['repo_name']}/settings/secrets/actions\n\n";
    
    echo "测试完成！\n";
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "错误堆栈:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
