#!/usr/bin/env php
<?php

require_once __DIR__ . '/../vendor/autoload.php';

use App\Services\UploadQueueService;
use App\Models\UploadRecord;

// 检查参数
if ($argc < 2) {
    echo "用法: php process-single-upload.php <upload_id>\n";
    exit(1);
}

$uploadId = $argv[1];

echo "开始处理单个上传任务: {$uploadId}\n";

try {
    // 获取上传记录
    $uploadRecord = new UploadRecord();
    $upload = $uploadRecord->findById($uploadId);
    
    if (!$upload) {
        echo "上传记录不存在: {$uploadId}\n";
        exit(1);
    }
    
    // 检查状态，只处理pending状态的任务
    if ($upload['status'] !== 'pending') {
        echo "任务状态不是pending，当前状态: {$upload['status']}\n";
        exit(0);
    }
    
    // 创建队列服务并处理任务
    $queueService = new UploadQueueService();
    $queueService->processSingleUpload($upload);
    
    echo "任务处理完成: {$uploadId}\n";
    
} catch (\Exception $e) {
    echo "处理任务失败: {$e->getMessage()}\n";
    
    // 更新任务状态为失败
    try {
        $uploadRecord = new UploadRecord();
        $uploadRecord->updateStatus($uploadId, 'failed', $e->getMessage());
    } catch (\Exception $updateError) {
        echo "更新任务状态失败: {$updateError->getMessage()}\n";
    }
    
    exit(1);
}
