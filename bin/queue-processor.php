#!/usr/bin/env php
<?php

require_once __DIR__ . '/../vendor/autoload.php';

use App\Services\UploadQueueService;

echo "启动上传队列处理器...\n";

// 创建队列服务
$queueService = new UploadQueueService();

// 处理队列的主循环
while (true) {
    try {
        echo "检查待处理的上传任务...\n";
        $queueService->processQueue();

        // 等待5秒后再次检查
        sleep(5);
    } catch (\Exception $e) {
        echo "队列处理错误: " . $e->getMessage() . "\n";
        echo "等待10秒后重试...\n";
        sleep(10);
    }
}
