<?php

namespace App\Controllers;

use App\Services\AuthService;
use App\Services\UploadService;
use App\Services\UploadQueueService;
use App\Models\UploadRecord;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;

class UploadController
{
    private AuthService $authService;
    private UploadService $uploadService;

    public function __construct()
    {
        $this->authService = new AuthService();
        $this->uploadService = new UploadService();
    }

    /**
     * 上传IPA文件 - 支持JSON请求
     */
    public function uploadIPA(Request $request): JsonResponse
    {
        error_log('uploadIPA: 开始处理上传任务创建请求');

        // 验证token
        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            error_log('uploadIPA: 用户未授权');
            return new JsonResponse([
                'success' => false,
                'error' => '未授权访问'
            ], 401);
        }

        error_log('uploadIPA: 用户已授权，用户ID: ' . $user['_id']);

        try {
            // 获取JSON数据
            $data = json_decode($request->getContent(), true);
            error_log('uploadIPA: 接收到的数据: ' . json_encode($data));

            if (!$data) {
                error_log('uploadIPA: 无效的请求数据');
                return new JsonResponse([
                    'success' => false,
                    'error' => '无效的请求数据'
                ], 400);
            }

            // 验证必需字段
            $requiredFields = ['auth_method', 'file_name', 'file_size'];
            foreach ($requiredFields as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    return new JsonResponse([
                        'success' => false,
                        'error' => "缺少必需字段: $field"
                    ], 400);
                }
            }

            // 验证认证信息
            $authMethod = $data['auth_method'];
            if ($authMethod === 'api_key') {
                $requiredAuthFields = ['api_key_id', 'issuer_id', 'api_key_content'];
                foreach ($requiredAuthFields as $field) {
                    if (!isset($data[$field]) || empty($data[$field])) {
                        return new JsonResponse([
                            'success' => false,
                            'error' => "缺少API Key字段: $field"
                        ], 400);
                    }
                }
            } elseif ($authMethod === 'apple_id') {
                $requiredAuthFields = ['apple_id', 'app_password'];
                foreach ($requiredAuthFields as $field) {
                    if (!isset($data[$field]) || empty($data[$field])) {
                        return new JsonResponse([
                            'success' => false,
                            'error' => "缺少Apple ID字段: $field"
                        ], 400);
                    }
                }
            } else {
                return new JsonResponse([
                    'success' => false,
                    'error' => '无效的认证方式'
                ], 400);
            }

            // 创建简化的上传记录（详细信息将在工作流中填充）
            $uploadRecord = new UploadRecord();
            $recordData = [
                'user_id' => $user['_id'],
                'filename' => $data['file_name'],
                'file_size' => $data['file_size'],
                'auth_method' => $authMethod,
                'auth_data' => $this->prepareAuthData($data, $authMethod),
                'release_notes' => $data['release_notes'] ?? '',
                'status' => 'pending'
            ];

            // 如果提供了文件路径，保存到记录中
            if (isset($data['file_path']) && !empty($data['file_path'])) {
                $recordData['temp_file_path'] = $data['file_path'];
            }

            $uploadId = $uploadRecord->createSimple($recordData);

            if (!$uploadId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => '创建上传记录失败'
                ], 500);
            }

            // 立即开始处理上传任务
            $this->processUploadImmediately((string)$uploadId);

            return new JsonResponse([
                'success' => true,
                'message' => '上传任务已创建并开始处理',
                'upload_id' => $uploadId
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => '上传失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取用户上传记录
     */
    public function getRecords(Request $request): JsonResponse
    {
        $token = $request->headers->get('Authorization');
        if (!$token || !str_starts_with($token, 'Bearer ')) {
            return new JsonResponse(['error' => '未授权访问'], 401);
        }
        $token = substr($token, 7);
        $user = (new AuthService())->verifyToken($token);
        if (!$user) {
            return new JsonResponse(['error' => '无效token'], 401);
        }
        $userId = $user['_id'];
        $role = $user['role'] ?? 'user';
        $all = $request->query->get('all');
        $uploadRecordModel = new \App\Models\UploadRecord();
        if ($role === 'admin' && $all) {
            // 管理员查所有记录
            $records = $uploadRecordModel->getAllRecordsWithUser();
        } else {
            // 普通用户查自己的
            $records = $uploadRecordModel->findByUserId($userId);
        }
        return new JsonResponse(['success' => true, 'records' => $records]);
    }

    /**
     * 获取上传记录详情
     */
    public function getRecordDetail(Request $request, string $recordId): JsonResponse
    {
        // 验证token
        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            return new JsonResponse([
                'success' => false,
                'error' => '未授权访问'
            ], 401);
        }

        $record = $this->uploadService->getRecordDetail($recordId);

        if (!$record) {
            return new JsonResponse([
                'success' => false,
                'error' => '记录不存在'
            ], 404);
        }

        // 检查权限（普通用户只能查看自己的记录）
        if ($user['role'] !== 'admin' && $record['user_id'] !== $user['_id']) {
            return new JsonResponse([
                'success' => false,
                'error' => '权限不足'
            ], 403);
        }

        return new JsonResponse([
            'success' => true,
            'record' => $record
        ], 200);
    }

    /**
     * 获取队列状态
     */
    public function getQueueStatus(Request $request): JsonResponse
    {
        // 验证token
        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            return new JsonResponse([
                'success' => false,
                'error' => '未授权访问'
            ], 401);
        }

        $status = $this->uploadService->getQueueStatus();

        return new JsonResponse([
            'success' => true,
            'status' => $status
        ], 200);
    }

    /**
     * 获取认证用户信息
     */
    private function getAuthenticatedUser(Request $request): ?array
    {
        $token = $request->headers->get('Authorization');

        if (!$token) {
            return null;
        }

        // 移除Bearer前缀
        $token = str_replace('Bearer ', '', $token);

        return $this->authService->verifyToken($token);
    }

    /**
     * 准备认证数据
     */
    private function prepareAuthData(array $data, string $authMethod): array
    {
        if ($authMethod === 'api_key') {
            return [
                'api_key_id' => $data['api_key_id'],
                'issuer_id' => $data['issuer_id'],
                'api_key_content' => $data['api_key_content']
            ];
        } else {
            return [
                'apple_id' => $data['apple_id'],
                'app_password' => $data['app_password']
            ];
        }
    }

    /**
     * 上传文件到服务器
     */
    public function uploadFile(Request $request): JsonResponse
    {
        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            return new JsonResponse(['error' => '未授权'], 401);
        }

        try {
            // 检查是否有上传的文件
            if (!isset($_FILES['ipa_file']) || $_FILES['ipa_file']['error'] !== UPLOAD_ERR_OK) {
                return new JsonResponse([
                    'success' => false,
                    'error' => '没有上传文件或上传失败'
                ], 400);
            }

            $file = $_FILES['ipa_file'];

            // 验证文件类型
            if (!str_ends_with(strtolower($file['name']), '.ipa')) {
                return new JsonResponse([
                    'success' => false,
                    'error' => '只支持.ipa格式的文件'
                ], 400);
            }

            // 创建上传目录
            $uploadDir = '/tmp/ipa_uploads/' . $user['_id'];
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // 生成唯一文件名
            $fileName = uniqid() . '_' . $file['name'];
            $filePath = $uploadDir . '/' . $fileName;

            // 移动文件
            if (!move_uploaded_file($file['tmp_name'], $filePath)) {
                return new JsonResponse([
                    'success' => false,
                    'error' => '文件保存失败'
                ], 500);
            }

            return new JsonResponse([
                'success' => true,
                'file_path' => $filePath,
                'file_name' => $file['name'],
                'file_size' => $file['size']
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => '文件上传失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 解析IPA文件信息
     */
    public function parseIPA(Request $request): JsonResponse
    {
        error_log('parseIPA: 开始处理IPA解析请求');

        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            error_log('parseIPA: 用户未授权');
            return new JsonResponse(['error' => '未授权'], 401);
        }

        error_log('parseIPA: 用户已授权，用户ID: ' . $user['_id']);

        try {
            // 检查是否有上传的文件
            error_log('parseIPA: 检查上传文件，$_FILES内容: ' . json_encode($_FILES));

            if (!isset($_FILES['ipa_file']) || $_FILES['ipa_file']['error'] !== UPLOAD_ERR_OK) {
                error_log('parseIPA: 没有上传文件或上传失败，error: ' . ($_FILES['ipa_file']['error'] ?? 'N/A'));
                return new JsonResponse([
                    'success' => false,
                    'error' => '没有上传文件或上传失败'
                ], 400);
            }

            $file = $_FILES['ipa_file'];
            error_log('parseIPA: 文件信息 - name: ' . $file['name'] . ', size: ' . $file['size']);

            // 验证文件类型
            if (!str_ends_with(strtolower($file['name']), '.ipa')) {
                return new JsonResponse([
                    'success' => false,
                    'error' => '只支持.ipa格式的文件'
                ], 400);
            }

            // 创建临时存储目录
            $tempDir = '/tmp/ipa_uploads/' . $user['_id'];
            if (!is_dir($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            // 生成唯一文件名
            $fileName = uniqid() . '_' . $file['name'];
            $filePath = $tempDir . '/' . $fileName;

            // 移动文件到临时目录
            if (!move_uploaded_file($file['tmp_name'], $filePath)) {
                return new JsonResponse([
                    'success' => false,
                    'error' => '文件保存失败'
                ], 500);
            }

            // 暂时返回基本的文件信息（真实解析将在服务端工作流中进行）
            $ipaInfo = $this->getBasicIPAInfo($file['name'], $file['size']);

            return new JsonResponse([
                'success' => true,
                'info' => $ipaInfo,
                'file_path' => $filePath,  // 返回文件路径给前端
                'file_name' => $file['name'],
                'file_size' => $file['size']
            ]);
        } catch (\Exception $e) {
            error_log('IPA parsing exception: ' . $e->getMessage());
            error_log('Exception trace: ' . $e->getTraceAsString());

            return new JsonResponse([
                'success' => false,
                'error' => '解析IPA文件失败: ' . $e->getMessage()
            ], 500);
        } catch (\Error $e) {
            error_log('IPA parsing error: ' . $e->getMessage());
            error_log('Error trace: ' . $e->getTraceAsString());

            return new JsonResponse([
                'success' => false,
                'error' => '解析IPA文件时发生错误: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * GitHub工作流webhook回调
     */
    public function webhook(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);

            if (!$data || !isset($data['upload_id']) || !isset($data['status'])) {
                return new JsonResponse(['error' => '无效的webhook数据'], 400);
            }

            $uploadId = $data['upload_id'];
            $status = $data['status'];

            // 更新上传记录状态
            $uploadRecord = new UploadRecord();
            $updateData = [
                'status' => $status === 'success' ? 'completed' : 'failed',
                'updated_at' => new \MongoDB\BSON\UTCDateTime()
            ];

            if ($status === 'success') {
                $updateData['completed_at'] = new \MongoDB\BSON\UTCDateTime();
                $updateData['upload_result'] = $data['result'] ?? null;
            } else {
                $updateData['error'] = $data['error'] ?? '上传失败';
            }

            $uploadRecord->updateById($uploadId, $updateData);

            // 这里可以通过WebSocket通知前端
            // 暂时只记录日志
            error_log("Webhook received for upload {$uploadId}: {$status}");

            return new JsonResponse(['success' => true, 'message' => 'Webhook处理成功']);
        } catch (\Exception $e) {
            error_log('Webhook处理失败: ' . $e->getMessage());
            return new JsonResponse(['error' => 'Webhook处理失败'], 500);
        }
    }

    /**
     * 获取基本的IPA信息（用于前端展示）
     */
    private function getBasicIPAInfo(string $fileName, int $fileSize): array
    {
        // 从文件名提取应用名称
        $appName = pathinfo($fileName, PATHINFO_FILENAME);

        return [
            'name' => $appName,
            'bundle_id' => '待解析', // 将在服务端工作流中解析
            'version' => '待解析',   // 将在服务端工作流中解析
            'build' => '待解析',     // 将在服务端工作流中解析
            'icon' => null,
            'file_size' => $fileSize
        ];
    }

    /**
     * 提取IPA文件信息
     */
    private function extractIPAInfo(string $ipaPath): array
    {
        $tempDir = sys_get_temp_dir() . '/ipa_extract_' . uniqid();

        try {
            // 创建临时目录
            if (!mkdir($tempDir, 0755, true)) {
                throw new \Exception('无法创建临时目录');
            }

            // 解压IPA文件
            $zip = new \ZipArchive();
            if ($zip->open($ipaPath) !== TRUE) {
                throw new \Exception('无法打开IPA文件');
            }

            $zip->extractTo($tempDir);
            $zip->close();

            // 查找Payload目录
            $payloadDir = $tempDir . '/Payload';
            if (!is_dir($payloadDir)) {
                throw new \Exception('IPA文件格式错误：找不到Payload目录');
            }

            // 查找.app目录
            $appDirs = glob($payloadDir . '/*.app');
            if (empty($appDirs)) {
                throw new \Exception('IPA文件格式错误：找不到.app目录');
            }

            $appDir = $appDirs[0];
            $infoPlistPath = $appDir . '/Info.plist';

            if (!file_exists($infoPlistPath)) {
                throw new \Exception('找不到Info.plist文件');
            }

            // 解析Info.plist
            $plistContent = file_get_contents($infoPlistPath);
            error_log('Info.plist content length: ' . strlen($plistContent));

            $plist = $this->parsePlist($plistContent);
            error_log('Parsed plist keys: ' . implode(', ', array_keys($plist)));

            // 提取应用信息
            $info = [
                'name' => $plist['CFBundleDisplayName'] ?? $plist['CFBundleName'] ?? 'Unknown',
                'bundle_id' => $plist['CFBundleIdentifier'] ?? 'Unknown',
                'version' => $plist['CFBundleShortVersionString'] ?? 'Unknown',
                'build' => $plist['CFBundleVersion'] ?? 'Unknown',
                'icon' => null
            ];

            error_log('Extracted info: ' . json_encode($info));

            // 尝试提取应用图标
            $iconPath = $this->extractAppIcon($appDir, $plist);
            if ($iconPath) {
                // 将图标转换为base64
                $iconData = file_get_contents($iconPath);
                $info['icon'] = 'data:image/png;base64,' . base64_encode($iconData);
            }

            return $info;
        } finally {
            // 清理临时目录
            $this->removeDirectory($tempDir);
        }
    }

    /**
     * 解析plist文件
     */
    private function parsePlist(string $content): array
    {
        $result = [];

        // 检查是否是二进制plist格式
        if (substr($content, 0, 8) === 'bplist00') {
            error_log('Detected binary plist format');
            return $this->parseBinaryPlist($content);
        }

        // 尝试使用SimpleXML解析XML格式的plist
        try {
            $xml = simplexml_load_string($content);
            if ($xml !== false) {
                $result = $this->parseSimpleXMLPlist($xml);
                if (!empty($result)) {
                    error_log('SimpleXML parsed ' . count($result) . ' key-value pairs');
                    return $result;
                }
            }
        } catch (\Exception $e) {
            error_log('SimpleXML parsing failed: ' . $e->getMessage());
        }

        // 备用方案：正则表达式解析XML格式
        // 匹配 <key>name</key><string>value</string> 模式
        preg_match_all('/<key>(.*?)<\/key>\s*<string>(.*?)<\/string>/s', $content, $matches, PREG_SET_ORDER);

        foreach ($matches as $match) {
            $key = trim($match[1]);
            $value = trim($match[2]);
            $result[$key] = $value;
        }

        // 如果没有找到任何键值对，尝试更宽松的匹配
        if (empty($result)) {
            // 匹配可能有换行的情况
            preg_match_all('/<key>\s*(.*?)\s*<\/key>\s*<string>\s*(.*?)\s*<\/string>/s', $content, $matches, PREG_SET_ORDER);

            foreach ($matches as $match) {
                $key = trim($match[1]);
                $value = trim($match[2]);
                $result[$key] = $value;
            }
        }

        error_log('Regex parsed ' . count($result) . ' key-value pairs');
        return $result;
    }

    /**
     * 解析二进制plist文件
     */
    private function parseBinaryPlist(string $content): array
    {
        $result = [];

        try {
            // 创建临时文件
            $tempFile = tempnam(sys_get_temp_dir(), 'bplist_');
            file_put_contents($tempFile, $content);

            // 尝试使用plutil转换为XML格式
            $xmlFile = $tempFile . '.xml';
            $command = "plutil -convert xml1 -o " . escapeshellarg($xmlFile) . " " . escapeshellarg($tempFile) . " 2>/dev/null";
            exec($command, $output, $returnCode);

            if ($returnCode === 0 && file_exists($xmlFile)) {
                $xmlContent = file_get_contents($xmlFile);
                error_log('Binary plist converted to XML, length: ' . strlen($xmlContent));

                // 解析转换后的XML
                $xml = simplexml_load_string($xmlContent);
                if ($xml !== false) {
                    $result = $this->parseSimpleXMLPlist($xml);
                }

                unlink($xmlFile);
            } else {
                error_log('plutil conversion failed, trying manual parsing');
                // 如果plutil不可用，尝试手动解析关键字段
                $result = $this->parseBasicBinaryPlist($content);
            }

            unlink($tempFile);
        } catch (\Exception $e) {
            error_log('Binary plist parsing failed: ' . $e->getMessage());
        }

        error_log('Binary plist parsed ' . count($result) . ' key-value pairs');
        return $result;
    }

    /**
     * 基础的二进制plist解析（提取常见字段）
     */
    private function parseBasicBinaryPlist(string $content): array
    {
        $result = [];

        // 简化的解析方法：直接从二进制数据中提取可读字符串
        // 然后根据上下文判断哪些是字段值

        // 提取所有可能的字符串
        preg_match_all('/[\x20-\x7E\x80-\xFF]{3,50}/', $content, $matches);
        $strings = array_unique($matches[0]);

        // 查找应用名称
        foreach ($strings as $string) {
            $clean = trim($string);
            if (strlen($clean) >= 3 && strlen($clean) <= 30) {
                // 跳过明显的字段名
                if (
                    strpos($clean, 'CFBundle') !== false ||
                    strpos($clean, 'UI') === 0 ||
                    strpos($clean, 'DT') === 0 ||
                    strpos($clean, 'LS') === 0 ||
                    strpos($clean, 'NS') === 0
                ) {
                    continue;
                }

                // 跳过包含下划线或特殊字符的字符串
                if (
                    strpos($clean, '_') !== false ||
                    strpos($clean, '[') !== false ||
                    strpos($clean, ']') !== false
                ) {
                    continue;
                }

                // 检查是否是Bundle ID格式
                if (preg_match('/^[a-zA-Z0-9.-]+\.[a-zA-Z0-9.-]+$/', $clean)) {
                    $result['CFBundleIdentifier'] = $clean;
                    error_log("Found CFBundleIdentifier: $clean");
                    continue;
                }

                // 检查是否是版本号格式
                if (preg_match('/^\d+(\.\d+)*$/', $clean)) {
                    if (!isset($result['CFBundleShortVersionString'])) {
                        $result['CFBundleShortVersionString'] = $clean;
                        error_log("Found CFBundleShortVersionString: $clean");
                    } elseif (!isset($result['CFBundleVersion'])) {
                        $result['CFBundleVersion'] = $clean;
                        error_log("Found CFBundleVersion: $clean");
                    }
                    continue;
                }

                // 检查是否是应用名称（包含字母，可能包含中文）
                if (preg_match('/[\p{L}]/u', $clean) && !isset($result['CFBundleDisplayName'])) {
                    $result['CFBundleDisplayName'] = $clean;
                    error_log("Found CFBundleDisplayName: $clean");
                    continue;
                }
            }
        }

        // 如果没有找到DisplayName，尝试使用CFBundleName
        if (!isset($result['CFBundleDisplayName']) && isset($result['CFBundleName'])) {
            $result['CFBundleDisplayName'] = $result['CFBundleName'];
        }

        return $result;
    }



    /**
     * 使用SimpleXML解析plist
     */
    private function parseSimpleXMLPlist(\SimpleXMLElement $xml): array
    {
        $result = [];

        // 查找dict元素
        $dict = $xml->dict ?? null;
        if (!$dict) {
            return $result;
        }

        $children = $dict->children();
        $key = null;

        foreach ($children as $child) {
            if ($child->getName() === 'key') {
                $key = (string)$child;
            } elseif ($key && $child->getName() === 'string') {
                $result[$key] = (string)$child;
                $key = null;
            }
        }

        return $result;
    }

    /**
     * 提取应用图标
     */
    private function extractAppIcon(string $appDir, array $plist): ?string
    {
        // 常见的图标文件名
        $iconNames = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            'Icon.png'
        ];

        foreach ($iconNames as $iconName) {
            $iconPath = $appDir . '/' . $iconName;
            if (file_exists($iconPath)) {
                return $iconPath;
            }
        }

        return null;
    }

    /**
     * 递归删除目录
     */
    private function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->removeDirectory($path);
            } else {
                unlink($path);
            }
        }
        rmdir($dir);
    }

    /**
     * 获取上传进度
     */
    public function getProgress(Request $request): JsonResponse
    {
        try {
            // 验证用户身份
            $token = $request->headers->get('Authorization');
            if (!$token || !str_starts_with($token, 'Bearer ')) {
                return new JsonResponse([
                    'success' => false,
                    'error' => '未授权访问'
                ], 401);
            }

            $token = substr($token, 7);
            $user = $this->authService->verifyToken($token);
            if (!$user) {
                return new JsonResponse([
                    'success' => false,
                    'error' => '无效token'
                ], 401);
            }

            $uploadId = $request->attributes->get('uploadId');
            if (!$uploadId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => '缺少上传ID'
                ], 400);
            }

            // 查询上传记录
            $uploadRecord = new UploadRecord();
            $record = $uploadRecord->findById($uploadId);
            if (!$record) {
                return new JsonResponse([
                    'success' => false,
                    'error' => '上传记录不存在'
                ], 404);
            }

            // 检查权限（只能查看自己的记录，管理员可以查看所有）
            if ($user['role'] !== 'admin' && $record['user_id'] !== $user['_id']) {
                return new JsonResponse([
                    'success' => false,
                    'error' => '无权限访问此记录'
                ], 403);
            }

            // 构造进度数据
            $progress = [
                'upload_id' => $uploadId,
                'status' => $record['status'],
                'progress' => $record['progress'] ?? 0,
                'step' => $this->getStepFromStatus($record['status']),
                'message' => $this->getProgressMessage($record),
                'error' => $record['error'] ?? null,
                'created_at' => $record['created_at'],
                'updated_at' => $record['updated_at'] ?? null
            ];

            return new JsonResponse([
                'success' => true,
                'progress' => $progress
            ]);
        } catch (\Exception $e) {
            error_log("获取上传进度失败: " . $e->getMessage());
            return new JsonResponse([
                'success' => false,
                'error' => '获取进度失败'
            ], 500);
        }
    }

    /**
     * 根据状态获取对应的步骤
     */
    private function getStepFromStatus(string $status): string
    {
        switch ($status) {
            case 'pending':
                return 'step-validate';
            case 'processing':
                return 'step-github';
            case 'uploading':
                return 'step-upload-file';
            case 'completed':
                return 'step-testflight';
            case 'failed':
                return 'step-validate';
            default:
                return 'step-validate';
        }
    }

    /**
     * 根据状态获取状态消息
     */
    private function getStatusMessage(string $status): string
    {
        switch ($status) {
            case 'pending':
                return '等待处理中...';
            case 'processing':
                return '正在处理上传任务...';
            case 'uploading':
                return '正在上传到TestFlight...';
            case 'completed':
                return '上传完成！';
            case 'failed':
                return '上传失败';
            default:
                return '未知状态';
        }
    }

    /**
     * 获取进度消息（优先显示具体错误信息）
     */
    private function getProgressMessage(array $record): string
    {
        $status = $record['status'] ?? 'unknown';

        // 如果是失败状态且有具体错误信息，优先显示错误信息
        if ($status === 'failed' && !empty($record['error'])) {
            return $record['error'];
        }

        // 如果有状态消息，使用状态消息
        if (!empty($record['status_message'])) {
            return $record['status_message'];
        }

        // 否则使用默认状态消息
        return $this->getStatusMessage($status);
    }

    /**
     * 立即处理上传任务（异步执行）
     */
    private function processUploadImmediately(string $uploadId): void
    {
        // 在后台异步执行任务处理
        $command = "php " . __DIR__ . "/../../bin/process-single-upload.php " . escapeshellarg($uploadId) . " > /dev/null 2>&1 &";
        exec($command);
    }

    /**
     * 下载IPA文件
     */
    public function downloadIPA(Request $request): Response
    {
        try {
            $recordId = $request->attributes->get('recordId');
            if (!$recordId) {
                return new JsonResponse(['error' => '缺少记录ID'], 400);
            }

            // 验证用户权限
            $user = $this->getAuthenticatedUser($request);
            if (!$user) {
                return new JsonResponse(['error' => '未授权访问'], 401);
            }

            // 获取上传记录
            $uploadRecord = new UploadRecord();
            $record = $uploadRecord->findById($recordId);

            if (!$record) {
                return new JsonResponse(['error' => '记录不存在'], 404);
            }

            // 检查权限：只有记录所有者或管理员可以下载
            if ($record['user_id'] !== $user['_id'] && $user['role'] !== 'admin') {
                return new JsonResponse(['error' => '无权限访问'], 403);
            }

            // 检查文件是否存在
            $filePath = $this->getIPAFilePath($record);
            if (!$filePath || !file_exists($filePath)) {
                return new JsonResponse(['error' => 'IPA文件不存在'], 404);
            }

            // 返回文件下载响应
            $response = new Response();
            $response->headers->set('Content-Type', 'application/octet-stream');
            $response->headers->set('Content-Disposition', 'attachment; filename="' . ($record['filename'] ?? 'app.ipa') . '"');
            $response->headers->set('Content-Length', filesize($filePath));
            $response->setContent(file_get_contents($filePath));

            return $response;
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => '下载失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 工作流状态回调接口
     */
    public function workflowCallback(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);

            if (!$data) {
                return new JsonResponse(['error' => '无效的JSON数据'], 400);
            }

            // 验证必需字段
            $requiredFields = ['upload_id', 'status'];
            foreach ($requiredFields as $field) {
                if (!isset($data[$field])) {
                    return new JsonResponse(['error' => "缺少必需字段: {$field}"], 400);
                }
            }

            $uploadId = $data['upload_id'];
            $status = $data['status'];
            $step = $data['step'] ?? null;
            $progress = $data['progress'] ?? null;
            $message = $data['message'] ?? '';
            $error = $data['error'] ?? null;
            $result = $data['result'] ?? null;

            // 更新上传记录
            $uploadRecord = new UploadRecord();
            $record = $uploadRecord->findById($uploadId);

            if (!$record) {
                return new JsonResponse(['error' => '上传记录不存在'], 404);
            }

            // 根据状态更新记录
            $updateData = [
                'updated_at' => new \MongoDB\BSON\UTCDateTime()
            ];

            switch ($status) {
                case 'started':
                    $updateData['status'] = 'processing';
                    $updateData['started_at'] = new \MongoDB\BSON\UTCDateTime();
                    break;

                case 'progress':
                    // 只更新进度信息，不改变状态
                    if ($step) $updateData['current_step'] = $step;
                    if ($progress !== null) $updateData['progress'] = $progress;
                    if ($message) $updateData['status_message'] = $message;
                    break;

                case 'success':
                case 'completed':
                    $updateData['status'] = 'completed';
                    $updateData['completed_at'] = new \MongoDB\BSON\UTCDateTime();
                    $updateData['progress'] = 100;
                    if ($result) $updateData['upload_result'] = $result;
                    break;

                case 'failed':
                case 'error':
                    $updateData['status'] = 'failed';
                    $updateData['error'] = $error ?: $message;
                    break;

                default:
                    return new JsonResponse(['error' => '无效的状态值'], 400);
            }

            // 执行更新
            $success = $uploadRecord->updateById($uploadId, $updateData);

            if (!$success) {
                return new JsonResponse(['error' => '更新记录失败'], 500);
            }

            // 广播进度更新（如果有WebSocket连接）
            if ($status === 'progress' && $step && $progress !== null) {
                $this->broadcastProgress($uploadId, [
                    'step' => $step,
                    'progress' => $progress,
                    'status' => $message
                ]);
            }

            return new JsonResponse([
                'success' => true,
                'message' => '状态更新成功'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => '状态更新失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取IPA文件路径
     */
    private function getIPAFilePath(array $record): ?string
    {
        // 优先使用stored_filename
        if (!empty($record['stored_filename'])) {
            $storagePath = $_ENV['IPA_STORAGE_PATH'] ?? '/data/storage/ipa';
            return $storagePath . '/' . $record['stored_filename'];
        }

        // 降级到临时文件路径
        if (!empty($record['temp_file_path']) && file_exists($record['temp_file_path'])) {
            return $record['temp_file_path'];
        }

        return null;
    }

    /**
     * 生成临时下载链接
     */
    public function generateTempDownloadLink(Request $request): JsonResponse
    {
        try {
            $recordId = $request->attributes->get('recordId');
            if (!$recordId) {
                return new JsonResponse(['error' => '缺少记录ID'], 400);
            }

            // 获取上传记录
            $uploadRecord = new UploadRecord();
            $record = $uploadRecord->findById($recordId);

            if (!$record) {
                return new JsonResponse(['error' => '记录不存在'], 404);
            }

            // 检查文件是否存在
            $filePath = $this->getIPAFilePath($record);
            if (!$filePath || !file_exists($filePath)) {
                return new JsonResponse(['error' => 'IPA文件不存在'], 404);
            }

            // 生成临时token（有效期30分钟）
            $tempToken = $this->generateTempToken($recordId);

            // 构造临时下载链接
            $downloadUrl = "https://api.ios.xxyx.cn/api/upload/temp-download/{$recordId}?token={$tempToken}";

            return new JsonResponse([
                'success' => true,
                'download_url' => $downloadUrl,
                'expires_in' => 1800, // 30分钟
                'file_name' => $record['filename'] ?? 'app.ipa'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => '生成下载链接失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 临时下载接口（无需认证，但需要临时token）
     */
    public function tempDownload(Request $request): Response
    {
        try {
            $recordId = $request->attributes->get('recordId');
            $token = $request->query->get('token');

            if (!$recordId || !$token) {
                return new JsonResponse(['error' => '缺少必需参数'], 400);
            }

            // 验证临时token
            if (!$this->verifyTempToken($recordId, $token)) {
                return new JsonResponse(['error' => '无效或已过期的下载链接'], 403);
            }

            // 获取上传记录
            $uploadRecord = new UploadRecord();
            $record = $uploadRecord->findById($recordId);

            if (!$record) {
                return new JsonResponse(['error' => '记录不存在'], 404);
            }

            // 检查文件是否存在
            $filePath = $this->getIPAFilePath($record);
            if (!$filePath || !file_exists($filePath)) {
                return new JsonResponse(['error' => 'IPA文件不存在'], 404);
            }

            // 返回文件下载响应
            $response = new Response();
            $response->headers->set('Content-Type', 'application/octet-stream');
            $response->headers->set('Content-Disposition', 'attachment; filename="' . ($record['filename'] ?? 'app.ipa') . '"');
            $response->headers->set('Content-Length', filesize($filePath));
            $response->setContent(file_get_contents($filePath));

            return $response;
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => '下载失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 生成临时token
     */
    private function generateTempToken(string $recordId): string
    {
        $payload = [
            'record_id' => $recordId,
            'expires' => time() + 1800, // 30分钟后过期
            'type' => 'temp_download'
        ];

        return base64_encode(json_encode($payload) . '|' . hash_hmac('sha256', json_encode($payload), $_ENV['APP_SECRET'] ?? 'default_secret'));
    }

    /**
     * 验证临时token
     */
    private function verifyTempToken(string $recordId, string $token): bool
    {
        try {
            $decoded = base64_decode($token);
            $parts = explode('|', $decoded);

            if (count($parts) !== 2) {
                return false;
            }

            $payload = json_decode($parts[0], true);
            $signature = $parts[1];

            if (!$payload) {
                return false;
            }

            // 验证签名
            $expectedSignature = hash_hmac('sha256', $parts[0], $_ENV['APP_SECRET'] ?? 'default_secret');
            if (!hash_equals($expectedSignature, $signature)) {
                return false;
            }

            // 验证记录ID
            if ($payload['record_id'] !== $recordId) {
                return false;
            }

            // 验证过期时间
            if ($payload['expires'] < time()) {
                return false;
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 广播进度更新
     */
    private function broadcastProgress(string $uploadId, array $progressData): void
    {
        // 这里可以实现WebSocket广播或其他实时通信机制
        // 暂时只记录日志
        error_log("Progress update for {$uploadId}: " . json_encode($progressData));
    }
}
