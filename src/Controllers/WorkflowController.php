<?php

namespace App\Controllers;

use App\Models\UploadRecord;
use App\Services\UploadService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;

class WorkflowController
{
    private UploadRecord $uploadRecordModel;
    private UploadService $uploadService;

    public function __construct()
    {
        $this->uploadRecordModel = new UploadRecord();
        $this->uploadService = new UploadService();
    }

    /**
     * 处理工作流回调
     */
    public function callback(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (!$data) {
            return new JsonResponse([
                'success' => false,
                'error' => '无效的请求数据'
            ], 400);
        }

        $recordId = $data['record_id'] ?? '';
        $status = $data['status'] ?? '';
        $result = $data['result'] ?? [];
        $error = $data['error'] ?? null;

        if (empty($recordId)) {
            return new JsonResponse([
                'success' => false,
                'error' => '缺少记录ID'
            ], 400);
        }

        // 查找上传记录
        $record = $this->uploadRecordModel->findById($recordId);
        if (!$record) {
            return new JsonResponse([
                'success' => false,
                'error' => '记录不存在'
            ], 404);
        }

        // 更新记录状态
        $updateData = [];

        if ($status === 'success') {
            $updateData['upload_result'] = $result;
            $this->uploadRecordModel->updateStatus($recordId, 'success', $updateData);
        } else {
            $updateData['upload_result'] = [
                'error' => $error ? $error['message'] : '上传失败',
                'type' => $error ? $error['type'] : 'unknown'
            ];
            $this->uploadRecordModel->updateStatus($recordId, 'failed', $updateData);
        }

        // 处理队列中的下一个任务
        $this->uploadService->processUploadQueue();

        return new JsonResponse([
            'success' => true,
            'message' => '回调处理成功'
        ], 200);
    }

    /**
     * 下载IPA文件（工作流专用）
     */
    public function downloadIPA(Request $request, string $recordId): Response
    {
        // 验证记录ID格式
        if (!preg_match('/^[a-f0-9]{24}$/', $recordId)) {
            return new Response('Invalid record ID', 400);
        }

        // 查找上传记录
        $record = $this->uploadRecordModel->findById($recordId);
        if (!$record) {
            return new Response('Record not found', 404);
        }

        // 检查文件是否存在
        $filePath = $this->getIPAFilePath($record['stored_filename']);
        if (!file_exists($filePath)) {
            return new Response('File not found', 404);
        }

        // 设置响应头
        $response = new Response();
        $response->headers->set('Content-Type', 'application/octet-stream');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $record['filename'] . '"');
        $response->headers->set('Content-Length', filesize($filePath));

        // 读取文件内容
        $content = file_get_contents($filePath);
        $response->setContent($content);

        return $response;
    }

    /**
     * 获取IPA文件路径
     */
    private function getIPAFilePath(string $storedFilename): string
    {
        $config = \App\Config\AppConfig::getInstance();
        return $config->get('ipa_storage_path') . '/' . $storedFilename;
    }
}
