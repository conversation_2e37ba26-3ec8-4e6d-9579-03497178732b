<?php

namespace App\Controllers;

use App\Services\AuthService;
use App\Services\UploadService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;

class AuthController
{
    private AuthService $authService;
    private UploadService $uploadService;

    public function __construct()
    {
        $this->authService = new AuthService();
        $this->uploadService = new UploadService();
    }

    /**
     * 用户登录
     */
    public function login(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (!$data) {
            return new JsonResponse([
                'success' => false,
                'error' => '无效的请求数据'
            ], 400);
        }

        $username = $data['username'] ?? $data['email'] ?? '';
        $password = $data['password'] ?? '';

        if (empty($username) || empty($password)) {
            return new JsonResponse([
                'success' => false,
                'error' => '用户名/邮箱和密码不能为空'
            ], 400);
        }

        $result = $this->authService->login($username, $password);

        if ($result['success']) {
            return new JsonResponse($result, 200);
        } else {
            return new JsonResponse($result, 401);
        }
    }

    /**
     * 激活账号
     */
    public function activate(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (!$data) {
            return new JsonResponse([
                'success' => false,
                'error' => '无效的请求数据'
            ], 400);
        }

        $username = $data['username'] ?? '';
        $activationCode = $data['activation_code'] ?? '';

        if (empty($username) || empty($activationCode)) {
            return new JsonResponse([
                'success' => false,
                'error' => '用户名和激活码不能为空'
            ], 400);
        }

        $result = $this->authService->activate($username, $activationCode);

        if ($result['success']) {
            return new JsonResponse($result, 200);
        } else {
            return new JsonResponse($result, 400);
        }
    }

    /**
     * 验证token
     */
    public function verify(Request $request): JsonResponse
    {
        $token = $request->headers->get('Authorization');

        if (!$token) {
            return new JsonResponse([
                'success' => false,
                'error' => '缺少认证token'
            ], 401);
        }

        // 移除Bearer前缀
        $token = str_replace('Bearer ', '', $token);

        $user = $this->authService->verifyToken($token);

        if ($user) {
            return new JsonResponse([
                'success' => true,
                'user' => $user
            ], 200);
        } else {
            return new JsonResponse([
                'success' => false,
                'error' => 'token无效或已过期'
            ], 401);
        }
    }

    /**
     * 刷新token
     */
    public function refresh(Request $request): JsonResponse
    {
        $token = $request->headers->get('Authorization');

        if (!$token) {
            return new JsonResponse([
                'success' => false,
                'error' => '缺少认证token'
            ], 401);
        }

        // 移除Bearer前缀
        $token = str_replace('Bearer ', '', $token);

        $newToken = $this->authService->refreshToken($token);

        if ($newToken) {
            return new JsonResponse([
                'success' => true,
                'token' => $newToken
            ], 200);
        } else {
            return new JsonResponse([
                'success' => false,
                'error' => 'token刷新失败'
            ], 401);
        }
    }

    /**
     * 验证API Key格式
     */
    public function validateAPIKey(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (!$data) {
            return new JsonResponse([
                'success' => false,
                'error' => '无效的请求数据'
            ], 400);
        }

        $apiKeyId = $data['api_key_id'] ?? '';
        $issuerId = $data['issuer_id'] ?? '';
        $apiKeyContent = $data['api_key_content'] ?? '';

        $result = $this->authService->validateAPIKey($apiKeyId, $issuerId, $apiKeyContent);

        return new JsonResponse($result, $result['valid'] ? 200 : 400);
    }

    /**
     * 验证Apple ID格式
     */
    public function validateAppleID(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (!$data) {
            return new JsonResponse([
                'success' => false,
                'error' => '无效的请求数据'
            ], 400);
        }

        $appleId = $data['apple_id'] ?? '';
        $appPassword = $data['app_password'] ?? '';

        $result = $this->authService->validateAppleID($appleId, $appPassword);

        return new JsonResponse($result, $result['valid'] ? 200 : 400);
    }
}
