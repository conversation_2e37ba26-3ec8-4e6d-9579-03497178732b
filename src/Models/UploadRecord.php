<?php

namespace App\Models;

use App\Database\MongoDB;
use MongoDB\BSON\ObjectId;
use MongoDB\Collection;

class UploadRecord
{
    private Collection $collection;
    private MongoDB $db;

    public function __construct()
    {
        $this->db = MongoDB::getInstance();
        $this->collection = $this->db->getCollection('upload_records');
    }

    public function create(array $data): ObjectId
    {
        $recordData = [
            'user_id' => new ObjectId($data['user_id']),
            'filename' => $data['filename'],
            'stored_filename' => $data['stored_filename'],
            'bundle_id' => $data['bundle_id'],
            'app_name' => $data['app_name'],
            'version' => $data['version'],
            'build' => $data['build'],
            'icon_path' => $data['icon_path'] ?? null,
            'file_size' => $data['file_size'],
            'auth_method' => $data['auth_method'],
            'auth_data' => $data['auth_data'] ?? null,
            'github_account' => new ObjectId($data['github_account']),
            'status' => $data['status'] ?? 'queued',
            'queue_position' => $data['queue_position'] ?? null,
            'workflow_id' => $data['workflow_id'] ?? null,
            'upload_result' => $data['upload_result'] ?? null,
            'created_at' => new \MongoDB\BSON\UTCDateTime(),
            'started_at' => null,
            'completed_at' => null
        ];

        $result = $this->collection->insertOne($recordData);
        return $result->getInsertedId();
    }

    /**
     * 创建简化的上传记录（只包含前端提供的基本信息）
     */
    public function createSimple(array $data): ObjectId
    {
        $recordData = [
            'user_id' => new ObjectId($data['user_id']),
            'filename' => $data['filename'],
            'file_size' => $data['file_size'],
            'auth_method' => $data['auth_method'],
            'auth_data' => $data['auth_data'],
            'release_notes' => $data['release_notes'] ?? '',
            'status' => $data['status'] ?? 'pending',

            // 临时文件路径（如果提供）
            'temp_file_path' => $data['temp_file_path'] ?? null,

            // 以下字段将在工作流中填充
            'stored_filename' => null,
            'bundle_id' => null,
            'app_name' => null,
            'version' => null,
            'build' => null,
            'icon_path' => null,
            'github_account' => null,
            'queue_position' => null,
            'workflow_id' => null,
            'upload_result' => null,

            // 时间戳
            'created_at' => new \MongoDB\BSON\UTCDateTime(),
            'started_at' => null,
            'completed_at' => null
        ];

        $result = $this->collection->insertOne($recordData);
        return $result->getInsertedId();
    }

    public function findById(string $id): ?array
    {
        $record = $this->collection->findOne(['_id' => new ObjectId($id)]);
        return $record ? $this->documentToArray($record) : null;
    }

    public function findByUserId(string $userId, int $limit = 50, int $skip = 0): array
    {
        $cursor = $this->collection->find(
            ['user_id' => new ObjectId($userId)],
            [
                'limit' => $limit,
                'skip' => $skip,
                'sort' => ['created_at' => -1]
            ]
        );

        $records = [];
        foreach ($cursor as $record) {
            $records[] = $this->documentToArray($record);
        }

        return $records;
    }

    public function getQueuedRecords(): array
    {
        $cursor = $this->collection->find(
            ['status' => 'queued'],
            ['sort' => ['created_at' => 1]]
        );

        $records = [];
        foreach ($cursor as $record) {
            $records[] = $this->documentToArray($record);
        }

        return $records;
    }

    public function getUploadingRecords(): array
    {
        $cursor = $this->collection->find(['status' => 'uploading']);

        $records = [];
        foreach ($cursor as $record) {
            $records[] = $this->documentToArray($record);
        }

        return $records;
    }

    public function updateStatus(string $recordId, string $status, array $additionalData = []): bool
    {
        $updateData = ['status' => $status];

        if ($status === 'uploading') {
            $updateData['started_at'] = new \MongoDB\BSON\UTCDateTime();
        } elseif (in_array($status, ['success', 'failed'])) {
            $updateData['completed_at'] = new \MongoDB\BSON\UTCDateTime();
        }

        if (!empty($additionalData)) {
            $updateData = array_merge($updateData, $additionalData);
        }

        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($recordId)],
            ['$set' => $updateData]
        );
        return $result->getModifiedCount() > 0;
    }

    public function updateQueuePosition(string $recordId, int $position): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($recordId)],
            ['$set' => ['queue_position' => $position]]
        );
        return $result->getModifiedCount() > 0;
    }

    public function updateWorkflowId(string $recordId, string $workflowId): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($recordId)],
            ['$set' => ['workflow_id' => $workflowId]]
        );
        return $result->getModifiedCount() > 0;
    }

    public function updateUploadResult(string $recordId, array $result): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($recordId)],
            ['$set' => ['upload_result' => $result]]
        );
        return $result->getModifiedCount() > 0;
    }

    public function countRecords(array $filter = []): int
    {
        return $this->collection->countDocuments($filter);
    }

    public function countQueuedRecords(): int
    {
        return $this->collection->countDocuments(['status' => 'queued']);
    }

    public function countUploadingRecords(): int
    {
        return $this->collection->countDocuments(['status' => 'uploading']);
    }

    public function getNextQueuedRecord(): ?array
    {
        $record = $this->collection->findOne(
            ['status' => 'queued'],
            ['sort' => ['created_at' => 1]]
        );

        return $record ? $this->documentToArray($record) : null;
    }

    public function deleteRecord(string $recordId): bool
    {
        $result = $this->collection->deleteOne(['_id' => new ObjectId($recordId)]);
        return $result->getDeletedCount() > 0;
    }

    public function getAllRecordsWithUser()
    {
        $collection = $this->db->getCollection('upload_records');
        $userCollection = $this->db->getCollection('users');
        $records = iterator_to_array($collection->find([], ['sort' => ['created_at' => -1]]));
        // 关联用户名
        foreach ($records as &$rec) {
            if (isset($rec['user_id'])) {
                $user = $userCollection->findOne(['_id' => $rec['user_id']]);
                $rec['username'] = $user['username'] ?? '';
            }
        }
        return array_map([$this, 'documentToArray'], $records);
    }

    private function documentToArray($document): array
    {
        $data = $document->getArrayCopy();

        // 转换ObjectId为字符串
        if (isset($data['_id']) && $data['_id'] instanceof ObjectId) {
            $data['_id'] = (string) $data['_id'];
        }

        if (isset($data['user_id']) && $data['user_id'] instanceof ObjectId) {
            $data['user_id'] = (string) $data['user_id'];
        }

        if (isset($data['github_account']) && $data['github_account'] instanceof ObjectId) {
            $data['github_account'] = (string) $data['github_account'];
        }

        // 转换日期为ISO字符串
        if (isset($data['created_at']) && $data['created_at'] instanceof \MongoDB\BSON\UTCDateTime) {
            $data['created_at'] = $data['created_at']->toDateTime()->format('c');
        }

        if (isset($data['started_at']) && $data['started_at'] instanceof \MongoDB\BSON\UTCDateTime) {
            $data['started_at'] = $data['started_at']->toDateTime()->format('c');
        }

        if (isset($data['completed_at']) && $data['completed_at'] instanceof \MongoDB\BSON\UTCDateTime) {
            $data['completed_at'] = $data['completed_at']->toDateTime()->format('c');
        }

        return $data;
    }

    /**
     * 查找待处理的上传任务
     */
    public function findPendingUploads(int $limit = 10): array
    {
        $cursor = $this->collection->find(
            ['status' => 'pending'],
            [
                'limit' => $limit,
                'sort' => ['created_at' => 1] // 按创建时间升序
            ]
        );

        $uploads = [];
        foreach ($cursor as $document) {
            $uploads[] = $this->documentToArray($document);
        }

        return $uploads;
    }

    /**
     * 根据GitHub账号统计使用次数
     */
    public function countByGitHubAccount(string $githubAccountId): int
    {
        return $this->collection->countDocuments([
            'github_account' => new ObjectId($githubAccountId)
        ]);
    }

    /**
     * 更新上传记录
     */
    public function updateById(string $id, array $updateData): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($id)],
            ['$set' => $updateData]
        );

        return $result->getModifiedCount() > 0;
    }
}
