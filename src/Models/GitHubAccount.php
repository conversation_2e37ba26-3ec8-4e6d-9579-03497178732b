<?php

namespace App\Models;

use App\Database\MongoDB;
use MongoDB\BSON\ObjectId;
use MongoDB\Collection;

class GitHubAccount
{
    private Collection $collection;
    private MongoDB $db;

    public function __construct()
    {
        $this->db = MongoDB::getInstance();
        $this->collection = $this->db->getCollection('github_accounts');
    }

    public function create(array $data): ObjectId
    {
        $accountData = [
            'username' => $data['username'],
            'email' => $data['email'],
            'token' => $data['token'],
            'remaining_minutes' => $data['remaining_minutes'] ?? 0,
            'status' => $data['status'] ?? 'active',
            'last_check' => new \MongoDB\BSON\UTCDateTime(),
            'created_at' => new \MongoDB\BSON\UTCDateTime()
        ];

        $result = $this->collection->insertOne($accountData);
        return $result->getInsertedId();
    }

    public function findById(string $id): ?array
    {
        $account = $this->collection->findOne(['_id' => new ObjectId($id)]);
        return $account ? $this->documentToArray($account) : null;
    }

    public function findByUsername(string $username): ?array
    {
        $account = $this->collection->findOne(['username' => $username]);
        return $account ? $this->documentToArray($account) : null;
    }

    public function getAllAccounts(): array
    {
        $cursor = $this->collection->find(
            ['status' => 'active'],
            ['sort' => ['remaining_minutes' => -1]]
        );

        $accounts = [];
        foreach ($cursor as $account) {
            $accounts[] = $this->documentToArray($account);
        }

        return $accounts;
    }

    public function getBestAccount(): ?array
    {
        // 优先选择状态为active且未被使用的账号
        // 如果没有未使用的，则选择使用时间最少的

        // 首先尝试找到未使用的账号
        $account = $this->collection->findOne(
            [
                'status' => 'active',
                'is_in_use' => ['$ne' => true]
            ],
            [
                'sort' => ['last_used_at' => 1], // 按最后使用时间升序
                'limit' => 1
            ]
        );

        // 如果没有未使用的账号，选择使用时间最少的
        if (!$account) {
            $account = $this->collection->findOne(
                ['status' => 'active'],
                [
                    'sort' => ['total_usage_minutes' => 1], // 按总使用时间升序
                    'limit' => 1
                ]
            );
        }

        return $account ? $this->documentToArray($account) : null;
    }

    public function updateRemainingMinutes(string $accountId, int $minutes): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($accountId)],
            [
                '$set' => [
                    'remaining_minutes' => $minutes,
                    'last_check' => new \MongoDB\BSON\UTCDateTime()
                ]
            ]
        );
        return $result->getModifiedCount() > 0;
    }

    public function updateStatus(string $accountId, string $status): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($accountId)],
            ['$set' => ['status' => $status]]
        );
        return $result->getModifiedCount() > 0;
    }

    public function deleteAccount(string $accountId): bool
    {
        $result = $this->collection->deleteOne(['_id' => new ObjectId($accountId)]);
        return $result->getDeletedCount() > 0;
    }

    public function countActiveAccounts(): int
    {
        return $this->collection->countDocuments(['status' => 'active']);
    }

    public function countAccounts(array $filter = []): int
    {
        return $this->collection->countDocuments($filter);
    }

    public function getAccountToken(string $accountId): ?string
    {
        $account = $this->collection->findOne(
            ['_id' => new ObjectId($accountId)],
            ['projection' => ['token' => 1]]
        );

        return $account ? $account->token : null;
    }

    /**
     * 标记账号为使用中
     */
    public function markAsInUse(string $accountId): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($accountId)],
            [
                '$set' => [
                    'is_in_use' => true,
                    'last_used_at' => new \MongoDB\BSON\UTCDateTime()
                ]
            ]
        );
        return $result->getModifiedCount() > 0;
    }

    /**
     * 标记账号为空闲
     */
    public function markAsIdle(string $accountId, int $usageMinutes = 0): bool
    {
        $updateData = [
            'is_in_use' => false,
            'last_used_at' => new \MongoDB\BSON\UTCDateTime()
        ];

        // 如果提供了使用分钟数，累加到总使用时间
        if ($usageMinutes > 0) {
            $updateData['$inc'] = ['total_usage_minutes' => $usageMinutes];
        }

        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($accountId)],
            ['$set' => $updateData]
        );
        return $result->getModifiedCount() > 0;
    }

    /**
     * 更新账号的GitHub API剩余分钟数
     */
    public function updateApiRemainingMinutes(string $accountId, int $remainingMinutes): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($accountId)],
            [
                '$set' => [
                    'api_remaining_minutes' => $remainingMinutes,
                    'last_api_check' => new \MongoDB\BSON\UTCDateTime()
                ]
            ]
        );
        return $result->getModifiedCount() > 0;
    }

    /**
     * 重置账号的使用分钟数
     */
    public function resetUsageMinutes(string $accountId, int $minutes = 0): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($accountId)],
            [
                '$set' => [
                    'total_usage_minutes' => $minutes,
                    'is_in_use' => false,
                    'last_used_at' => new \MongoDB\BSON\UTCDateTime()
                ]
            ]
        );
        return $result->getModifiedCount() > 0;
    }

    private function documentToArray($document): array
    {
        $data = $document->getArrayCopy();

        // 转换ObjectId为字符串
        if (isset($data['_id']) && $data['_id'] instanceof ObjectId) {
            $data['_id'] = (string) $data['_id'];
        }

        // 转换日期为ISO字符串
        if (isset($data['created_at']) && $data['created_at'] instanceof \MongoDB\BSON\UTCDateTime) {
            $data['created_at'] = $data['created_at']->toDateTime()->format('c');
        }

        if (isset($data['last_check']) && $data['last_check'] instanceof \MongoDB\BSON\UTCDateTime) {
            $data['last_check'] = $data['last_check']->toDateTime()->format('c');
        }

        // 不返回token字段，保护敏感信息
        unset($data['token']);

        return $data;
    }
}
