<?php

namespace App\Models;

use App\Database\MongoDB;
use MongoDB\BSON\ObjectId;
use MongoDB\Collection;

class User
{
    private Collection $collection;
    private MongoDB $db;

    public function __construct()
    {
        $this->db = MongoDB::getInstance();
        $this->collection = $this->db->getCollection('users');
    }

    public function create(array $data): ObjectId
    {
        $userData = [
            'username' => $data['username'],
            'email' => $data['email'],
            'password' => password_hash($data['password'], PASSWORD_DEFAULT),
            'role' => $data['role'] ?? 'user',
            'status' => $data['status'] ?? 'inactive',
            'created_at' => new \MongoDB\BSON\UTCDateTime(),
            'activated_at' => null,
            'last_login' => null
        ];

        $result = $this->collection->insertOne($userData);
        return $result->getInsertedId();
    }

    public function findByEmail(string $email): ?array
    {
        $user = $this->collection->findOne(['email' => $email]);
        return $user ? $this->documentToArray($user) : null;
    }

    public function findById(string $id): ?array
    {
        $user = $this->collection->findOne(['_id' => new ObjectId($id)]);
        return $user ? $this->documentToArray($user) : null;
    }

    public function findByUsername(string $username): ?array
    {
        $user = $this->collection->findOne(['username' => $username]);
        return $user ? $this->documentToArray($user) : null;
    }

    public function updateLastLogin(string $userId): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($userId)],
            ['$set' => ['last_login' => new \MongoDB\BSON\UTCDateTime()]]
        );
        return $result->getModifiedCount() > 0;
    }

    public function activate(string $userId): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($userId)],
            [
                '$set' => [
                    'status' => 'active',
                    'activated_at' => new \MongoDB\BSON\UTCDateTime()
                ]
            ]
        );
        return $result->getModifiedCount() > 0;
    }

    public function updateStatus(string $userId, string $status): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($userId)],
            ['$set' => ['status' => $status]]
        );
        return $result->getModifiedCount() > 0;
    }

    public function getAllUsers(int $limit = 50, int $skip = 0): array
    {
        $cursor = $this->collection->find(
            [],
            [
                'limit' => $limit,
                'skip' => $skip,
                'sort' => ['created_at' => -1]
            ]
        );

        $users = [];
        foreach ($cursor as $user) {
            $users[] = $this->documentToArray($user);
        }

        return $users;
    }

    public function countUsers(array $filter = []): int
    {
        return $this->collection->countDocuments($filter);
    }

    public function deleteUser(string $userId): bool
    {
        $result = $this->collection->deleteOne(['_id' => new ObjectId($userId)]);
        return $result->getDeletedCount() > 0;
    }

    public function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }

    private function documentToArray($document): array
    {
        $data = $document->getArrayCopy();

        // 转换ObjectId为字符串
        if (isset($data['_id']) && $data['_id'] instanceof ObjectId) {
            $data['_id'] = (string) $data['_id'];
        }

        // 转换日期为ISO字符串
        if (isset($data['created_at']) && $data['created_at'] instanceof \MongoDB\BSON\UTCDateTime) {
            $data['created_at'] = $data['created_at']->toDateTime()->format('c');
        }

        if (isset($data['activated_at']) && $data['activated_at'] instanceof \MongoDB\BSON\UTCDateTime) {
            $data['activated_at'] = $data['activated_at']->toDateTime()->format('c');
        }

        if (isset($data['last_login']) && $data['last_login'] instanceof \MongoDB\BSON\UTCDateTime) {
            $data['last_login'] = $data['last_login']->toDateTime()->format('c');
        }

        return $data;
    }
}
