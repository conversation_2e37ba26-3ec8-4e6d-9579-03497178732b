<?php

namespace App\Utils;

class IpaParser
{
    public static function parseInfoPlist(string $ipaPath): array
    {
        $tempPlist = sys_get_temp_dir() . "/temp_" . uniqid() . ".plist";

        try {
            echo "开始解析IPA文件: {$ipaPath}\n";

            if (!file_exists($ipaPath)) {
                throw new \Exception("IPA文件不存在: {$ipaPath}");
            }

            // 提取Info.plist文件
            echo "提取Info.plist文件...\n";
            $extractCmd = "unzip -p " . escapeshellarg($ipaPath) . " \"Payload/*.app/Info.plist\" > " . escapeshellarg($tempPlist);
            shell_exec($extractCmd . " 2>&1");

            if (!file_exists($tempPlist) || filesize($tempPlist) == 0) {
                throw new \Exception("无法提取Info.plist文件");
            }

            echo "Info.plist文件提取成功，大小: " . filesize($tempPlist) . " 字节\n";

            // 尝试使用plistutil转换为XML
            $plistData = null;
            if (self::commandExists("plistutil")) {
                echo "使用plistutil转换为XML格式...\n";
                $tempXmlPlist = $tempPlist . ".xml";
                $convertCmd = "plistutil -i " . escapeshellarg($tempPlist) . " -o " . escapeshellarg($tempXmlPlist) . " -f xml";
                $output = shell_exec($convertCmd . " 2>&1");

                if (file_exists($tempXmlPlist)) {
                    $xmlContent = file_get_contents($tempXmlPlist);
                    $plistData = self::parseXmlPlist($xmlContent);
                    unlink($tempXmlPlist); // 清理临时XML文件
                    echo "使用plistutil解析成功\n";
                } else {
                    echo "plistutil转换失败: $output\n";
                }
            }

            // 尝试使用plutil (macOS)
            if (!$plistData && self::commandExists("plutil")) {
                echo "使用plutil转换为JSON格式...\n";
                $convertCmd = "plutil -convert json " . escapeshellarg($tempPlist) . " -o -";
                $jsonOutput = shell_exec($convertCmd . " 2>&1");

                if ($jsonOutput && $jsonData = json_decode($jsonOutput, true)) {
                    $plistData = $jsonData;
                    echo "使用plutil解析成功\n";
                }
            }

            if (!$plistData) {
                throw new \Exception("无法解析plist文件，需要安装plistutil或plutil工具");
            }

            // 提取关键信息
            $bundleId = $plistData["CFBundleIdentifier"] ?? "unknown.bundle.id";
            $appName = $plistData["CFBundleDisplayName"]
                ?? $plistData["CFBundleName"]
                ?? pathinfo($ipaPath, PATHINFO_FILENAME);
            $version = $plistData["CFBundleShortVersionString"] ?? "1.0.0";
            $build = $plistData["CFBundleVersion"] ?? "1";

            echo "解析成功！\n";
            echo "Bundle ID: {$bundleId}\n";
            echo "应用名称: {$appName}\n";
            echo "版本号: {$version}\n";
            echo "构建号: {$build}\n";

            return [
                "bundle_id" => $bundleId,
                "app_name" => $appName,
                "version" => $version,
                "build" => $build,
                "minimum_os_version" => $plistData["MinimumOSVersion"] ?? "Unknown",
                "supported_devices" => $plistData["UIDeviceFamily"] ?? [],
                "required_capabilities" => $plistData["UIRequiredDeviceCapabilities"] ?? [],
                "raw_plist_data" => $plistData
            ];
        } catch (\Exception $e) {
            throw new \Exception("解析IPA文件失败: " . $e->getMessage());
        } finally {
            if (file_exists($tempPlist)) {
                unlink($tempPlist);
            }
        }
    }

    public static function isValidIpa(string $ipaPath): bool
    {
        try {
            $zipArchive = new \ZipArchive();
            if ($zipArchive->open($ipaPath) !== TRUE) {
                return false;
            }

            $hasPayload = false;
            for ($i = 0; $i < $zipArchive->numFiles; $i++) {
                $filename = $zipArchive->getNameIndex($i);
                if (strpos($filename, "Payload/") === 0 && strpos($filename, ".app/") !== false) {
                    $hasPayload = true;
                    break;
                }
            }

            $zipArchive->close();
            return $hasPayload;
        } catch (\Exception $e) {
            return false;
        }
    }

    public static function getIpaBasicInfo(string $ipaPath): array
    {
        $info = [
            "file_size" => filesize($ipaPath),
            "file_name" => basename($ipaPath),
            "is_valid" => false,
            "app_name" => null,
            "error" => null
        ];

        try {
            $zipArchive = new \ZipArchive();
            if ($zipArchive->open($ipaPath) !== TRUE) {
                $info["error"] = "无法打开IPA文件";
                return $info;
            }

            for ($i = 0; $i < $zipArchive->numFiles; $i++) {
                $filename = $zipArchive->getNameIndex($i);
                if (preg_match("/Payload\/([^\/]+\.app)\//", $filename, $matches)) {
                    $info["is_valid"] = true;
                    $info["app_name"] = str_replace(".app", "", $matches[1]);
                    break;
                }
            }

            $zipArchive->close();
        } catch (\Exception $e) {
            $info["error"] = $e->getMessage();
        }

        return $info;
    }

    private static function commandExists(string $command): bool
    {
        $result = shell_exec("which $command 2>/dev/null");
        return !empty($result);
    }

    /**
     * 解析XML格式的plist文件
     */
    private static function parseXmlPlist(string $xmlContent): ?array
    {
        try {
            $xml = simplexml_load_string($xmlContent);
            if ($xml === false) {
                return null;
            }

            // 查找dict元素
            $dict = $xml->dict;
            if (!$dict) {
                return null;
            }

            return self::parsePlistDict($dict);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 解析plist字典
     */
    private static function parsePlistDict($dict): array
    {
        $result = [];
        $keys = $dict->key;
        $values = [];

        // 收集所有值元素
        foreach ($dict->children() as $child) {
            if ($child->getName() !== "key") {
                $values[] = $child;
            }
        }

        // 配对键值
        for ($i = 0; $i < count($keys) && $i < count($values); $i++) {
            $key = (string)$keys[$i];
            $value = self::parsePlistValue($values[$i]);
            $result[$key] = $value;
        }

        return $result;
    }

    /**
     * 解析plist值
     */
    private static function parsePlistValue($element)
    {
        switch ($element->getName()) {
            case "string":
                return (string)$element;
            case "integer":
                return (int)$element;
            case "real":
                return (float)$element;
            case "true":
                return true;
            case "false":
                return false;
            case "array":
                $result = [];
                foreach ($element->children() as $child) {
                    $result[] = self::parsePlistValue($child);
                }
                return $result;
            case "dict":
                return self::parsePlistDict($element);
            default:
                return (string)$element;
        }
    }
}
