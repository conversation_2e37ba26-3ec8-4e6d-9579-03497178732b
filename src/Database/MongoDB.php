<?php

namespace App\Database;

use App\Config\AppConfig;
use MongoDB\Client;
use MongoDB\Database;
use MongoDB\Collection;

class MongoDB
{
    private static ?MongoDB $instance = null;
    private ?Client $client = null;
    private ?Database $database = null;
    private AppConfig $config;

    private function __construct()
    {
        $this->config = AppConfig::getInstance();
        $this->connect();
    }

    public static function getInstance(): MongoDB
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function connect(): void
    {
        try {
            $uri = $this->config->get('mongodb_uri');
            $this->client = new Client($uri);

            // 从URI中提取数据库名
            $databaseName = $this->extractDatabaseName($uri);
            $this->database = $this->client->selectDatabase($databaseName);
        } catch (\Exception $e) {
            throw new \RuntimeException('MongoDB连接失败: ' . $e->getMessage());
        }
    }

    private function extractDatabaseName(string $uri): string
    {
        // 从 mongodb://localhost:27017/xios 提取 xios
        $parts = parse_url($uri);
        return trim($parts['path'] ?? 'xios', '/');
    }

    public function getDatabase(): Database
    {
        return $this->database;
    }

    public function getCollection(string $collectionName): Collection
    {
        return $this->database->selectCollection($collectionName);
    }

    public function getClient(): Client
    {
        return $this->client;
    }

    public function ping(): bool
    {
        try {
            $this->database->command(['ping' => 1]);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
