<?php

namespace App\Services;

use App\Models\UploadRecord;
use App\Models\GitHubAccount;
use App\Services\GitHubService;
use App\Services\IPAParserService;

class UploadQueueService
{
    private $uploadRecord;
    private $githubAccount;
    private $githubService;
    private $ipaParser;

    public function __construct()
    {
        $this->uploadRecord = new UploadRecord();
        $this->githubAccount = new GitHubAccount();
        $this->githubService = new GitHubService();
        $this->ipaParser = new IPAParserService();
    }

    /**
     * 处理上传队列
     */
    public function processQueue()
    {
        echo "开始处理上传队列...\n";

        // 获取待处理的上传任务
        $pendingUploads = $this->uploadRecord->findPendingUploads();

        foreach ($pendingUploads as $upload) {
            try {
                $this->processUpload($upload);
            } catch (\Exception $e) {
                echo "处理上传失败: {$e->getMessage()}\n";
                $this->updateUploadStatus($upload['_id'], 'failed', $e->getMessage());
            }
        }
    }

    /**
     * 处理单个上传任务（公共方法）
     */
    public function processSingleUpload(array $upload)
    {
        $this->processUpload($upload);
    }

    /**
     * 处理单个上传任务
     */
    private function processUpload(array $upload)
    {
        $uploadId = (string)$upload['_id'];
        $githubAccount = null;
        $startTime = time();

        echo "处理上传任务: {$uploadId}\n";

        try {
            // 更新状态为处理中
            $this->updateUploadStatus($uploadId, 'processing');
            $this->broadcastProgress($uploadId, [
                'step' => 'step-validate',
                'progress' => 10,
                'status' => '验证认证信息...'
            ]);

            // 1. 验证认证信息
            $this->validateAuth($upload);

            // 2. 选择最优GitHub账号
            $this->broadcastProgress($uploadId, [
                'step' => 'step-github',
                'progress' => 30,
                'status' => '选择最优GitHub账号...'
            ]);

            $githubAccount = $this->selectBestGitHubAccount();
            echo "成功选择GitHub账号: {$githubAccount['username']}\n";

            // 3. 解析IPA文件
            $this->broadcastProgress($uploadId, [
                'step' => 'step-workflow',
                'progress' => 50,
                'status' => '解析IPA文件...'
            ]);
            $ipaInfo = $this->parseIPAFile($upload);

            // 4. 创建GitHub工作流
            $this->broadcastProgress($uploadId, [
                'step' => 'step-upload-file',
                'progress' => 70,
                'status' => '创建GitHub工作流...'
            ]);
            $workflowResult = $this->createGitHubWorkflow($upload, $githubAccount, $ipaInfo);

            // 5. 监控工作流执行
            $this->broadcastProgress($uploadId, [
                'step' => 'step-testflight',
                'progress' => 90,
                'status' => '上传到TestFlight...'
            ]);
            // 更新状态为处理中，等待工作流回调完成
            $this->updateUploadStatus($uploadId, 'processing', null, [
                'github_account' => $githubAccount['username'],
                'workflow_id' => $workflowResult['workflow_id'],
                'bundle_id' => $ipaInfo['bundle_id'],
                'app_name' => $ipaInfo['app_name'],
                'version' => $ipaInfo['version'],
                'build' => $ipaInfo['build']
            ]);

            // 监控工作流执行（不阻塞，通过回调更新最终状态）
            $this->monitorWorkflow($uploadId, $workflowResult);

            echo "上传任务完成: {$uploadId}\n";
        } catch (\Exception $e) {
            echo "上传任务失败: {$e->getMessage()}\n";
            $this->updateUploadStatus($uploadId, 'failed', $e->getMessage());
        } finally {
            // 释放GitHub账号
            if ($githubAccount) {
                $usageMinutes = intval((time() - $startTime) / 60); // 计算使用分钟数
                $this->githubAccount->markAsIdle($githubAccount['_id'], $usageMinutes);
                echo "释放GitHub账号: {$githubAccount['username']} (使用了 {$usageMinutes} 分钟)\n";
            }
        }
    }

    /**
     * 验证认证信息
     */
    private function validateAuth(array $upload)
    {
        $authData = $upload['auth_data'];
        $authMethod = $upload['auth_method'];

        if ($authMethod === 'api_key') {
            if (
                empty($authData['api_key_id']) ||
                empty($authData['issuer_id']) ||
                empty($authData['api_key_content'])
            ) {
                throw new \Exception('API Key信息不完整');
            }

            // 验证API Key格式
            if (!preg_match('/^[A-Z0-9]{10}$/', $authData['api_key_id'])) {
                throw new \Exception('API Key ID格式错误');
            }

            if (!preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $authData['issuer_id'])) {
                throw new \Exception('Issuer ID格式错误');
            }
        } elseif ($authMethod === 'apple_id') {
            if (empty($authData['apple_id']) || empty($authData['app_password'])) {
                throw new \Exception('Apple ID信息不完整');
            }

            // 验证Apple ID格式
            if (!filter_var($authData['apple_id'], FILTER_VALIDATE_EMAIL)) {
                throw new \Exception('Apple ID格式错误');
            }

            // 专属密码格式：xxxx-xxxx-xxxx-xxxx (字母和数字的组合)
            if (!preg_match('/^[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}$/', $authData['app_password'])) {
                throw new \Exception('专属密码格式错误，应为：xxxx-xxxx-xxxx-xxxx');
            }
        }
    }

    /**
     * 选择最优GitHub账号
     */
    private function selectBestGitHubAccount()
    {
        // 首先检查是否有活跃的GitHub账号
        $activeCount = $this->githubAccount->countActiveAccounts();
        echo "当前活跃GitHub账号数量: {$activeCount}\n";

        if ($activeCount === 0) {
            throw new \Exception('没有可用的GitHub账号，请联系管理员添加GitHub账号');
        }

        // 获取最佳账号（基于使用频率）
        $bestAccount = $this->githubAccount->getBestAccount();

        if (!$bestAccount) {
            throw new \Exception('无法获取可用的GitHub账号');
        }

        echo "选择GitHub账号: {$bestAccount['username']}\n";
        echo "  使用状态: " . ($bestAccount['is_in_use'] ? '使用中' : '空闲') . "\n";
        echo "  总使用分钟数: " . ($bestAccount['total_usage_minutes'] ?? 0) . "\n";

        // 使用本地使用时长记录系统进行账号管理
        echo "使用本地使用时长记录系统进行账号管理\n";

        // 检查本地记录的使用情况
        $totalUsage = $bestAccount['total_usage_minutes'] ?? 0;
        $maxUsageLimit = 2500; // 每个账号最多使用2500分钟（约42小时）
        $remainingMinutes = $maxUsageLimit - $totalUsage;

        echo "  使用限制: {$maxUsageLimit} 分钟\n";
        echo "  已使用: {$totalUsage} 分钟\n";
        echo "  剩余: {$remainingMinutes} 分钟\n";

        if ($totalUsage >= $maxUsageLimit) {
            throw new \Exception("GitHub账号 {$bestAccount['username']} 本地记录显示使用时间已达上限 (已使用: {$totalUsage}/{$maxUsageLimit} 分钟)");
        }

        if ($remainingMinutes < 60) {
            echo "警告: 账号 {$bestAccount['username']} 剩余时间不足1小时\n";
        }

        echo "本地记录检查通过，继续使用账号\n";

        // 标记账号为使用中
        $this->githubAccount->markAsInUse($bestAccount['_id']);
        echo "已标记账号为使用中\n";

        // 获取账号的token
        $token = $this->githubAccount->getAccountToken($bestAccount['_id']);
        if (!$token) {
            throw new \Exception("无法获取GitHub账号 {$bestAccount['username']} 的token");
        }

        // 将token添加到账号信息中
        $bestAccount['token'] = $token;

        return $bestAccount;
    }

    /**
     * 解析IPA文件
     */
    private function parseIPAFile(array $upload)
    {
        $uploadId = (string)$upload['_id'];

        // 1. 首先确保文件已经存储到永久位置
        $this->ensureFileStored($upload);

        // 2. 从数据库获取更新后的记录（包含解析的应用信息）
        $updatedUpload = $this->uploadRecord->findById($uploadId);

        if (!$updatedUpload) {
            throw new \Exception("找不到上传记录: {$uploadId}");
        }

        // 3. 返回解析的应用信息
        return [
            'bundle_id' => $updatedUpload['bundle_id'] ?? 'com.example.app',
            'app_name' => $updatedUpload['app_name'] ?? pathinfo($upload['filename'], PATHINFO_FILENAME),
            'version' => $updatedUpload['version'] ?? '1.0.0',
            'build' => $updatedUpload['build'] ?? '1',
            'minimum_os_version' => $updatedUpload['minimum_os_version'] ?? 'Unknown',
            'supported_devices' => $updatedUpload['supported_devices'] ?? [],
            'required_capabilities' => $updatedUpload['required_capabilities'] ?? []
        ];
    }

    /**
     * 确保文件已存储到永久位置
     */
    private function ensureFileStored(array $upload)
    {
        $uploadId = (string)$upload['_id'];

        // 检查是否已经有stored_filename
        if (!empty($upload['stored_filename'])) {
            return; // 文件已经存储
        }

        // 查找临时文件
        $tempFilePath = null;
        if (!empty($upload['temp_file_path']) && file_exists($upload['temp_file_path'])) {
            $tempFilePath = $upload['temp_file_path'];
        } else {
            // 尝试在临时目录中查找文件
            $tempDir = $_ENV['UPLOAD_TEMP_PATH'] ?? '/tmp';
            $possiblePaths = [
                $tempDir . '/' . $upload['filename'],
                $tempDir . '/' . $uploadId . '.ipa',
                '/tmp/' . $upload['filename'],
                '/tmp/' . $uploadId . '.ipa'
            ];

            foreach ($possiblePaths as $path) {
                if (file_exists($path)) {
                    $tempFilePath = $path;
                    break;
                }
            }
        }

        if (!$tempFilePath) {
            throw new \Exception("找不到上传的IPA文件");
        }

        // 创建存储目录
        $storageDir = $_ENV['IPA_STORAGE_PATH'] ?? '/data/storage/ipa';
        if (!is_dir($storageDir)) {
            mkdir($storageDir, 0755, true);
        }

        // 生成存储文件名
        $storedFilename = $uploadId . '_' . time() . '.ipa';
        $storedPath = $storageDir . '/' . $storedFilename;

        // 移动文件到永久存储位置
        if (!copy($tempFilePath, $storedPath)) {
            throw new \Exception("无法将文件移动到存储目录");
        }

        // 删除临时文件
        if (file_exists($tempFilePath)) {
            unlink($tempFilePath);
        }

        // 解析IPA文件信息
        $ipaInfo = $this->parseIPAFileFromPath($storedPath);

        // 更新数据库记录
        $this->uploadRecord->updateById($uploadId, [
            'stored_filename' => $storedFilename,
            'temp_file_path' => null,
            'bundle_id' => $ipaInfo['bundle_id'],
            'app_name' => $ipaInfo['app_name'],
            'version' => $ipaInfo['version'],
            'build' => $ipaInfo['build']
        ]);

        echo "文件已存储到: {$storedPath}\n";
        echo "应用信息: {$ipaInfo['app_name']} ({$ipaInfo['bundle_id']}) v{$ipaInfo['version']} build {$ipaInfo['build']}\n";
    }

    /**
     * 从IPA文件路径解析应用信息
     */
    private function parseIPAFileFromPath(string $ipaPath): array
    {
        try {
            echo "开始解析IPA文件: {$ipaPath}\n";

            // 使用IpaParser解析
            $ipaInfo = \App\Utils\IpaParser::parseInfoPlist($ipaPath);

            echo "解析成功！\n";
            echo "Bundle ID: {$ipaInfo['bundle_id']}\n";
            echo "应用名称: {$ipaInfo['app_name']}\n";
            echo "版本号: {$ipaInfo['version']}\n";
            echo "构建号: {$ipaInfo['build']}\n";
            echo "最低iOS版本: {$ipaInfo['minimum_os_version']}\n";

            return [
                'bundle_id' => $ipaInfo['bundle_id'],
                'app_name' => $ipaInfo['app_name'],
                'version' => $ipaInfo['version'],
                'build' => $ipaInfo['build'],
                'minimum_os_version' => $ipaInfo['minimum_os_version'],
                'supported_devices' => $ipaInfo['supported_devices'],
                'required_capabilities' => $ipaInfo['required_capabilities']
            ];
        } catch (\Exception $e) {
            echo "解析IPA文件失败: " . $e->getMessage() . "\n";

            // 尝试获取基本信息
            try {
                $basicInfo = \App\Utils\IpaParser::getIpaBasicInfo($ipaPath);
                $appName = $basicInfo['app_name'] ?: pathinfo($ipaPath, PATHINFO_FILENAME);
            } catch (\Exception $e2) {
                $appName = pathinfo($ipaPath, PATHINFO_FILENAME);
            }

            // 返回默认值
            return [
                'bundle_id' => 'unknown.bundle.id',
                'app_name' => $appName,
                'version' => '1.0.0',
                'build' => '1',
                'minimum_os_version' => 'Unknown',
                'supported_devices' => [],
                'required_capabilities' => []
            ];
        }
    }



    /**
     * 创建GitHub工作流
     */
    private function createGitHubWorkflow(array $upload, array $githubAccount, array $ipaInfo)
    {
        // 生成临时下载链接
        $tempDownloadUrl = $this->generateTempDownloadUrl((string)$upload['_id']);

        // 创建工作流配置
        $workflowConfig = [
            'upload_id' => (string)$upload['_id'],
            'bundle_id' => $ipaInfo['bundle_id'],
            'app_name' => $ipaInfo['app_name'],
            'version' => $ipaInfo['version'],
            'build' => $ipaInfo['build'],
            'auth_method' => $upload['auth_method'],
            'auth_data' => $upload['auth_data'],
            'release_notes' => $upload['release_notes'] ?? '',
            'download_url' => $tempDownloadUrl
        ];

        // 使用GitHub服务创建工作流
        return $this->githubService->createUploadWorkflow($githubAccount, $workflowConfig);
    }

    /**
     * 生成临时下载URL
     */
    private function generateTempDownloadUrl(string $uploadId): string
    {
        // 生成临时token（有效期30分钟）
        $payload = [
            'record_id' => $uploadId,
            'expires' => time() + 1800, // 30分钟后过期
            'type' => 'temp_download'
        ];

        $token = base64_encode(json_encode($payload) . '|' . hash_hmac('sha256', json_encode($payload), $_ENV['APP_SECRET'] ?? 'default_secret'));

        return "https://api.ios.xxyx.cn/api/upload/temp-download/{$uploadId}?token={$token}";
    }

    /**
     * 监控工作流执行
     */
    private function monitorWorkflow(string $uploadId, array $workflowResult)
    {
        // 这里应该监控GitHub Actions的执行状态
        // 暂时模拟等待
        sleep(2);
    }

    /**
     * 更新上传状态
     */
    private function updateUploadStatus(string $uploadId, string $status, ?string $error = null, ?array $result = null)
    {
        $updateData = [
            'status' => $status,
            'updated_at' => new \MongoDB\BSON\UTCDateTime()
        ];

        if ($error) {
            $updateData['error'] = $error;
        }

        if ($result) {
            $updateData = array_merge($updateData, $result);
        }

        if ($status === 'completed') {
            $updateData['completed_at'] = new \MongoDB\BSON\UTCDateTime();
        } elseif ($status === 'processing') {
            $updateData['started_at'] = new \MongoDB\BSON\UTCDateTime();
        }

        $this->uploadRecord->updateById($uploadId, $updateData);
    }

    /**
     * 记录进度更新
     */
    private function broadcastProgress(string $uploadId, array $progressData)
    {
        echo "进度更新 [{$uploadId}]: {$progressData['status']} ({$progressData['progress']}%)\n";
    }
}
