<?php

namespace App\Services;

use App\Config\AppConfig;
use App\Models\GitHubAccount;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class GitHubService
{
    private AppConfig $config;
    private Client $httpClient;
    private GitHubAccount $githubAccountModel;
    private WorkflowGenerator $workflowGenerator;

    public function __construct()
    {
        $this->config = AppConfig::getInstance();
        $this->httpClient = new Client([
            'timeout' => 30,
            'headers' => [
                'Accept' => 'application/vnd.github.v3+json',
                'User-Agent' => 'iOS-Tool-App'
            ]
        ]);
        $this->githubAccountModel = new GitHubAccount();
        $this->workflowGenerator = new WorkflowGenerator();
    }

    /**
     * 测试GitHub Token是否有效
     */
    public function testToken(string $token): array
    {
        try {
            $response = $this->httpClient->get('https://api.github.com/user', [
                'headers' => [
                    'Authorization' => 'token ' . $token
                ]
            ]);

            $userData = json_decode($response->getBody()->getContents(), true);

            return [
                'success' => true,
                'user' => $userData
            ];
        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;

            return [
                'success' => false,
                'error' => $this->getErrorMessage($statusCode),
                'status_code' => $statusCode
            ];
        }
    }

    /**
     * 获取GitHub Actions剩余时间
     */
    public function getRemainingMinutes(string $token): int
    {
        try {
            $response = $this->httpClient->get('https://api.github.com/rate_limit', [
                'headers' => [
                    'Authorization' => 'token ' . $token
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            // GitHub Actions的剩余时间
            if (isset($data['resources']['actions']['remaining'])) {
                return $data['resources']['actions']['remaining'];
            }

            return 0;
        } catch (RequestException $e) {
            // 如果无法获取，返回0
            return 0;
        }
    }

    /**
     * 检查GitHub账号是否有正在运行的工作流
     */
    public function hasRunningWorkflows(string $token, string $username, string $repoName): bool
    {
        try {
            $response = $this->httpClient->get("https://api.github.com/repos/{$username}/{$repoName}/actions/runs", [
                'headers' => [
                    'Authorization' => 'token ' . $token
                ],
                'query' => [
                    'status' => 'in_progress',
                    'per_page' => 1
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            return !empty($data['workflow_runs']);
        } catch (RequestException $e) {
            // 如果无法检查，假设没有运行中的工作流
            return false;
        }
    }

    /**
     * 创建GitHub Repository
     */
    public function createRepository(string $token, string $name, bool $isPrivate = true): array
    {
        try {
            $response = $this->httpClient->post('https://api.github.com/user/repos', [
                'headers' => [
                    'Authorization' => 'token ' . $token,
                    'Content-Type' => 'application/json'
                ],
                'json' => [
                    'name' => $name,
                    'description' => 'iOS App Uploader with GitHub Actions',
                    'private' => $isPrivate,
                    'auto_init' => true
                ]
            ]);

            $repoData = json_decode($response->getBody()->getContents(), true);

            return [
                'success' => true,
                'repository' => $repoData
            ];
        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
            $errorData = json_decode($e->getResponse()->getBody()->getContents(), true);

            return [
                'success' => false,
                'error' => $errorData['message'] ?? $this->getErrorMessage($statusCode),
                'status_code' => $statusCode
            ];
        }
    }

    /**
     * 上传文件到GitHub Repository
     */
    public function uploadFile(string $token, string $username, string $repoName, string $path, string $content, string $message): array
    {
        try {
            $response = $this->httpClient->put("https://api.github.com/repos/{$username}/{$repoName}/contents/{$path}", [
                'headers' => [
                    'Authorization' => 'token ' . $token,
                    'Content-Type' => 'application/json'
                ],
                'json' => [
                    'message' => $message,
                    'content' => base64_encode($content),
                    'branch' => 'main'
                ]
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            return [
                'success' => true,
                'result' => $result
            ];
        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
            $errorData = json_decode($e->getResponse()->getBody()->getContents(), true);

            return [
                'success' => false,
                'error' => $errorData['message'] ?? $this->getErrorMessage($statusCode),
                'status_code' => $statusCode
            ];
        }
    }

    /**
     * 触发GitHub Actions工作流
     */
    public function triggerWorkflow(string $token, string $username, string $repoName, string $workflowFile, array $inputs): array
    {
        try {
            $response = $this->httpClient->post("https://api.github.com/repos/{$username}/{$repoName}/actions/workflows/{$workflowFile}/dispatches", [
                'headers' => [
                    'Authorization' => 'token ' . $token,
                    'Content-Type' => 'application/json'
                ],
                'json' => [
                    'ref' => 'main',
                    'inputs' => $inputs
                ]
            ]);

            return [
                'success' => true,
                'status_code' => $response->getStatusCode()
            ];
        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
            $errorData = json_decode($e->getResponse()->getBody()->getContents(), true);

            return [
                'success' => false,
                'error' => $errorData['message'] ?? $this->getErrorMessage($statusCode),
                'status_code' => $statusCode
            ];
        }
    }

    /**
     * 获取最优的GitHub账号
     */
    public function getBestAccount(): ?array
    {
        $accounts = $this->githubAccountModel->getAllAccounts();

        if (empty($accounts)) {
            return null;
        }

        // 更新所有账号的剩余时间
        foreach ($accounts as $account) {
            $token = $this->githubAccountModel->getAccountToken($account['_id']);
            if ($token) {
                $remainingMinutes = $this->getRemainingMinutes($token);
                $this->githubAccountModel->updateRemainingMinutes($account['_id'], $remainingMinutes);
            }
        }

        // 重新获取排序后的账号
        $accounts = $this->githubAccountModel->getAllAccounts();

        // 返回剩余时间最多的账号
        return !empty($accounts) ? $accounts[0] : null;
    }

    /**
     * 更新所有GitHub账号的剩余时间
     */
    public function updateAllAccountMinutes(): void
    {
        $accounts = $this->githubAccountModel->getAllAccounts();

        foreach ($accounts as $account) {
            $token = $this->githubAccountModel->getAccountToken($account['_id']);
            if ($token) {
                $remainingMinutes = $this->getRemainingMinutes($token);
                $this->githubAccountModel->updateRemainingMinutes($account['_id'], $remainingMinutes);
            }
        }
    }

    /**
     * 获取错误信息
     */
    private function getErrorMessage(int $statusCode): string
    {
        switch ($statusCode) {
            case 401:
                return 'Token无效或已过期';
            case 403:
                return 'Token权限不足';
            case 404:
                return '资源不存在';
            case 422:
                return '请求参数错误';
            case 429:
                return '请求频率超限';
            default:
                return 'GitHub API请求失败';
        }
    }

    /**
     * 生成工作流文件名
     */
    public function generateWorkflowFileName(): string
    {
        return 'upload-testflight.yml';
    }

    /**
     * 生成Repository名称
     */
    public function generateRepoName(): string
    {
        return 'ios-uploader-' . uniqid();
    }

    /**
     * 创建上传工作流
     */
    public function createUploadWorkflow(array $githubAccount, array $config)
    {
        echo "开始创建上传工作流...\n";
        echo "GitHub账号: {$githubAccount['username']}\n";
        echo "配置: " . json_encode($config, JSON_UNESCAPED_UNICODE) . "\n";

        $repoName = $this->getOrCreateUploadRepo($githubAccount);
        echo "仓库名称: {$repoName}\n";

        $workflowFile = $this->generateWorkflowFile($config);
        echo "工作流文件长度: " . strlen($workflowFile) . " 字符\n";
        echo "工作流文件前100字符: " . substr($workflowFile, 0, 100) . "...\n";

        // 创建工作流文件
        echo "开始创建工作流文件...\n";
        $this->createWorkflowFile($githubAccount, $repoName, $workflowFile);

        // 设置仓库密钥
        echo "开始设置仓库密钥...\n";
        $this->setupRepositorySecrets($githubAccount, $repoName, $config);

        // 触发工作流
        echo "开始触发工作流...\n";
        $workflowId = $this->triggerUploadWorkflow($githubAccount, $repoName, $config);

        $result = [
            'repo_name' => $repoName,
            'workflow_id' => $workflowId,
            'github_url' => "https://github.com/{$githubAccount['username']}/{$repoName}/actions"
        ];

        echo "工作流创建完成: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
        return $result;
    }

    /**
     * 获取或创建上传仓库
     */
    private function getOrCreateUploadRepo(array $githubAccount)
    {
        $repoName = 'ios-upload-' . date('Y-m');

        try {
            // 检查仓库是否存在
            $response = $this->httpClient->get("https://api.github.com/repos/{$githubAccount['username']}/{$repoName}", [
                'headers' => [
                    'Authorization' => 'token ' . $githubAccount['token']
                ]
            ]);

            echo "仓库已存在: {$githubAccount['username']}/{$repoName}\n";
        } catch (RequestException $e) {
            if ($e->getResponse() && $e->getResponse()->getStatusCode() === 404) {
                // 创建新仓库
                $this->createUploadRepository($githubAccount, $repoName);
            } else {
                throw $e;
            }
        }

        return $repoName;
    }

    /**
     * 创建上传仓库
     */
    private function createUploadRepository(array $githubAccount, string $repoName)
    {
        $data = [
            'name' => $repoName,
            'description' => 'iOS应用自动上传到TestFlight',
            'private' => true,
            'auto_init' => true
        ];

        try {
            $response = $this->httpClient->post('https://api.github.com/user/repos', [
                'headers' => [
                    'Authorization' => 'token ' . $githubAccount['token']
                ],
                'json' => $data
            ]);

            echo "创建GitHub仓库: {$githubAccount['username']}/{$repoName}\n";
        } catch (RequestException $e) {
            throw new \Exception('创建GitHub仓库失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成工作流文件内容
     */
    private function generateWorkflowFile(array $config)
    {
        // 使用 WorkflowGenerator 生成工作流内容
        $workflowData = [
            'upload_id' => $config['upload_id'],
            'callback_url' => 'https://api.ios.xxyx.cn/api/upload/workflow-callback',
            'download_url' => $config['download_url'] ?? '',
            'bundle_id' => $config['bundle_id'] ?? '',
            'app_name' => $config['app_name'] ?? '',
            'version' => $config['version'] ?? '',
            'build' => $config['build'] ?? '',
            'release_notes' => $config['release_notes'] ?? ''
        ];

        if ($config['auth_method'] === 'api_key') {
            return $this->workflowGenerator->generateAdvancedAPIKeyWorkflow(
                array_merge($workflowData, $config['auth_data'])
            );
        } else {
            return $this->workflowGenerator->generateAdvancedAppleIDWorkflow(
                array_merge($workflowData, $config['auth_data'])
            );
        }
    }

    /**
     * 生成工作流文件内容（旧版本，保持向后兼容）
     * @deprecated 使用 generateWorkflowFile 替代
     */
    private function generateLegacyWorkflowFile(array $config)
    {
        $uploadId = $config['upload_id'];
        $callbackUrl = 'https://api.ios.xxyx.cn/api/upload/workflow-callback';
        $downloadUrl = $config['download_url'] ?? '';

        $authSteps = '';
        $uploadSteps = '';

        if ($config['auth_method'] === 'api_key') {
            $authSteps = '
    - name: Setup App Store Connect API Key
      run: |
        mkdir -p ~/private_keys
        echo "${{ secrets.API_KEY_CONTENT }}" > ~/private_keys/AuthKey_${{ secrets.API_KEY_ID }}.p8
        chmod 600 ~/private_keys/AuthKey_${{ secrets.API_KEY_ID }}.p8
        echo "API_KEY_ID=${{ secrets.API_KEY_ID }}" >> $GITHUB_ENV
        echo "ISSUER_ID=${{ secrets.ISSUER_ID }}" >> $GITHUB_ENV';

            $uploadSteps = '
    - name: Upload to TestFlight (API Key)
      run: |
        echo "开始上传到TestFlight (使用API Key)"

        # 上报开始状态
        curl -s -X POST ' . $callbackUrl . ' \\
          -H "Content-Type: application/json" \\
          -d \'{"upload_id": "' . $uploadId . '", "status": "progress", "step": "step-testflight", "progress": 80, "message": "开始上传到TestFlight..."}\'

        # 验证应用
        echo "验证应用..."
        if xcrun altool --validate-app \\
          -f "${IPA_FILE}" \\
          -t ios \\
          --apiKey "$API_KEY_ID" \\
          --apiIssuer "$ISSUER_ID" \\
          --verbose; then
          echo "应用验证通过"
        else
          echo "应用验证失败"
          curl -s -X POST ' . $callbackUrl . ' \\
            -H "Content-Type: application/json" \\
            -d \'{"upload_id": "' . $uploadId . '", "status": "failed", "error": "应用验证失败"}\'
          exit 1
        fi

        # 上传应用
        echo "上传应用到TestFlight..."
        curl -s -X POST ' . $callbackUrl . ' \\
          -H "Content-Type: application/json" \\
          -d \'{"upload_id": "' . $uploadId . '", "status": "progress", "step": "step-testflight", "progress": 90, "message": "正在上传到TestFlight..."}\'

        if xcrun altool --upload-app \\
          -f "${IPA_FILE}" \\
          -t ios \\
          --apiKey "$API_KEY_ID" \\
          --apiIssuer "$ISSUER_ID" \\
          --verbose; then
          echo "上传成功！"
        else
          echo "上传失败"
          curl -s -X POST ' . $callbackUrl . ' \\
            -H "Content-Type: application/json" \\
            -d \'{"upload_id": "' . $uploadId . '", "status": "failed", "error": "上传到TestFlight失败"}\'
          exit 1
        fi';
        }

        // 这部分代码已移动到 WorkflowGenerator 中
        // 保留此注释以说明重构历史
        return "# 此方法已重构，现在使用 WorkflowGenerator 生成工作流内容";
    }

    /**
     * 创建工作流文件
     */
    private function createWorkflowFile(array $githubAccount, string $repoName, string $workflowContent)
    {
        $path = '.github/workflows/upload-testflight.yml';
        echo "准备创建工作流文件: {$path}\n";
        echo "仓库: {$githubAccount['username']}/{$repoName}\n";
        echo "工作流内容长度: " . strlen($workflowContent) . " 字符\n";

        try {
            // 检查文件是否已存在
            echo "检查文件是否已存在...\n";
            $response = $this->httpClient->get("https://api.github.com/repos/{$githubAccount['username']}/{$repoName}/contents/{$path}", [
                'headers' => [
                    'Authorization' => 'token ' . $githubAccount['token']
                ]
            ]);

            $existingFile = json_decode($response->getBody()->getContents(), true);
            echo "文件已存在，准备更新...\n";

            // 文件已存在，更新
            $data = [
                'message' => 'Update TestFlight upload workflow',
                'content' => base64_encode($workflowContent),
                'sha' => $existingFile['sha']
            ];
        } catch (RequestException $e) {
            if ($e->getResponse() && $e->getResponse()->getStatusCode() === 404) {
                echo "文件不存在，准备创建新文件...\n";
                // 文件不存在，创建新文件
                $data = [
                    'message' => 'Add TestFlight upload workflow',
                    'content' => base64_encode($workflowContent)
                ];
            } else {
                echo "检查文件时发生错误: " . $e->getMessage() . "\n";
                throw $e;
            }
        }

        try {
            echo "发送请求到GitHub API...\n";
            $response = $this->httpClient->put("https://api.github.com/repos/{$githubAccount['username']}/{$repoName}/contents/{$path}", [
                'headers' => [
                    'Authorization' => 'token ' . $githubAccount['token']
                ],
                'json' => $data
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);
            echo "✅ 创建工作流文件成功: {$path}\n";
            echo "响应状态码: " . $response->getStatusCode() . "\n";
            echo "文件SHA: " . ($responseData['content']['sha'] ?? 'N/A') . "\n";
        } catch (RequestException $e) {
            $errorMessage = $e->getMessage();
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 'N/A';
            $responseBody = $e->getResponse() ? $e->getResponse()->getBody()->getContents() : 'N/A';

            echo "❌ 创建工作流文件失败:\n";
            echo "状态码: {$statusCode}\n";
            echo "错误信息: {$errorMessage}\n";
            echo "响应内容: {$responseBody}\n";

            throw new \Exception("创建工作流文件失败: {$errorMessage} (状态码: {$statusCode})");
        }
    }

    /**
     * 设置仓库密钥
     */
    private function setupRepositorySecrets(array $githubAccount, string $repoName, array $config)
    {
        echo "设置仓库密钥，认证方式: {$config['auth_method']}\n";
        $secrets = [];

        if ($config['auth_method'] === 'api_key') {
            echo "设置API Key相关密钥...\n";
            $secrets = [
                'API_KEY_ID' => $config['auth_data']['api_key_id'],
                'ISSUER_ID' => $config['auth_data']['issuer_id'],
                'API_KEY_CONTENT' => $config['auth_data']['api_key_content']
            ];
        } else {
            echo "设置Apple ID相关密钥...\n";
            $secrets = [
                'APPLE_ID' => $config['auth_data']['apple_id'],
                'APP_PASSWORD' => $config['auth_data']['app_password']
            ];
        }

        foreach ($secrets as $name => $value) {
            echo "创建密钥: {$name}\n";
            $this->createRepositorySecret($githubAccount, $repoName, $name, $value);
        }

        echo "仓库密钥设置完成\n";
    }

    /**
     * 创建仓库密钥
     */
    private function createRepositorySecret(array $githubAccount, string $repoName, string $name, string $value)
    {
        try {
            // 获取公钥
            $response = $this->httpClient->get("https://api.github.com/repos/{$githubAccount['username']}/{$repoName}/actions/secrets/public-key", [
                'headers' => [
                    'Authorization' => 'token ' . $githubAccount['token']
                ]
            ]);

            $publicKey = json_decode($response->getBody()->getContents(), true);

            // 使用libsodium进行正确的加密
            if (!function_exists('sodium_crypto_box_seal')) {
                throw new \Exception('需要安装libsodium扩展');
            }

            $publicKeyBinary = base64_decode($publicKey['key']);
            $encryptedValue = base64_encode(sodium_crypto_box_seal($value, $publicKeyBinary));

            // 创建密钥
            $data = [
                'encrypted_value' => $encryptedValue,
                'key_id' => $publicKey['key_id']
            ];

            $response = $this->httpClient->put("https://api.github.com/repos/{$githubAccount['username']}/{$repoName}/actions/secrets/{$name}", [
                'headers' => [
                    'Authorization' => 'token ' . $githubAccount['token']
                ],
                'json' => $data
            ]);

            echo "创建仓库密钥: {$name}\n";
        } catch (RequestException $e) {
            echo "创建密钥 {$name} 失败: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 触发上传工作流
     */
    private function triggerUploadWorkflow(array $githubAccount, string $repoName, array $config)
    {
        $data = [
            'ref' => 'main',
            'inputs' => [
                'upload_id' => $config['upload_id']
            ]
        ];

        echo "准备触发工作流...\n";
        echo "仓库: {$githubAccount['username']}/{$repoName}\n";
        echo "上传ID: {$config['upload_id']}\n";
        echo "触发数据: " . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n";

        try {
            $url = "https://api.github.com/repos/{$githubAccount['username']}/{$repoName}/actions/workflows/upload-testflight.yml/dispatches";
            echo "请求URL: {$url}\n";

            $response = $this->httpClient->post($url, [
                'headers' => [
                    'Authorization' => 'token ' . $githubAccount['token']
                ],
                'json' => $data
            ]);

            $statusCode = $response->getStatusCode();
            echo "✅ 触发GitHub工作流成功，状态码: {$statusCode}\n";

            $workflowId = uniqid('workflow_');
            echo "工作流ID: {$workflowId}\n";

            return $workflowId;
        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 'N/A';
            $responseBody = $e->getResponse() ? $e->getResponse()->getBody()->getContents() : 'N/A';

            echo "❌ 触发工作流失败:\n";
            echo "状态码: {$statusCode}\n";
            echo "错误信息: " . $e->getMessage() . "\n";
            echo "响应内容: {$responseBody}\n";

            throw new \Exception("触发工作流失败: " . $e->getMessage() . " (状态码: {$statusCode})");
        }
    }
}
