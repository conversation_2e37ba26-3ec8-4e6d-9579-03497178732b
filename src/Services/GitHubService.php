<?php

namespace App\Services;

use App\Config\AppConfig;
use App\Models\GitHubAccount;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class GitHubService
{
    private AppConfig $config;
    private Client $httpClient;
    private GitHubAccount $githubAccountModel;

    public function __construct()
    {
        $this->config = AppConfig::getInstance();
        $this->httpClient = new Client([
            'timeout' => 30,
            'headers' => [
                'Accept' => 'application/vnd.github.v3+json',
                'User-Agent' => 'iOS-Tool-App'
            ]
        ]);
        $this->githubAccountModel = new GitHubAccount();
    }

    /**
     * 测试GitHub Token是否有效
     */
    public function testToken(string $token): array
    {
        try {
            $response = $this->httpClient->get('https://api.github.com/user', [
                'headers' => [
                    'Authorization' => 'token ' . $token
                ]
            ]);

            $userData = json_decode($response->getBody()->getContents(), true);

            return [
                'success' => true,
                'user' => $userData
            ];
        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;

            return [
                'success' => false,
                'error' => $this->getErrorMessage($statusCode),
                'status_code' => $statusCode
            ];
        }
    }

    /**
     * 获取GitHub Actions剩余时间
     */
    public function getRemainingMinutes(string $token): int
    {
        try {
            $response = $this->httpClient->get('https://api.github.com/rate_limit', [
                'headers' => [
                    'Authorization' => 'token ' . $token
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            // GitHub Actions的剩余时间
            if (isset($data['resources']['actions']['remaining'])) {
                return $data['resources']['actions']['remaining'];
            }

            return 0;
        } catch (RequestException $e) {
            // 如果无法获取，返回0
            return 0;
        }
    }

    /**
     * 检查GitHub账号是否有正在运行的工作流
     */
    public function hasRunningWorkflows(string $token, string $username, string $repoName): bool
    {
        try {
            $response = $this->httpClient->get("https://api.github.com/repos/{$username}/{$repoName}/actions/runs", [
                'headers' => [
                    'Authorization' => 'token ' . $token
                ],
                'query' => [
                    'status' => 'in_progress',
                    'per_page' => 1
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            return !empty($data['workflow_runs']);
        } catch (RequestException $e) {
            // 如果无法检查，假设没有运行中的工作流
            return false;
        }
    }

    /**
     * 创建GitHub Repository
     */
    public function createRepository(string $token, string $name, bool $isPrivate = true): array
    {
        try {
            $response = $this->httpClient->post('https://api.github.com/user/repos', [
                'headers' => [
                    'Authorization' => 'token ' . $token,
                    'Content-Type' => 'application/json'
                ],
                'json' => [
                    'name' => $name,
                    'description' => 'iOS App Uploader with GitHub Actions',
                    'private' => $isPrivate,
                    'auto_init' => true
                ]
            ]);

            $repoData = json_decode($response->getBody()->getContents(), true);

            return [
                'success' => true,
                'repository' => $repoData
            ];
        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
            $errorData = json_decode($e->getResponse()->getBody()->getContents(), true);

            return [
                'success' => false,
                'error' => $errorData['message'] ?? $this->getErrorMessage($statusCode),
                'status_code' => $statusCode
            ];
        }
    }

    /**
     * 上传文件到GitHub Repository
     */
    public function uploadFile(string $token, string $username, string $repoName, string $path, string $content, string $message): array
    {
        try {
            $response = $this->httpClient->put("https://api.github.com/repos/{$username}/{$repoName}/contents/{$path}", [
                'headers' => [
                    'Authorization' => 'token ' . $token,
                    'Content-Type' => 'application/json'
                ],
                'json' => [
                    'message' => $message,
                    'content' => base64_encode($content),
                    'branch' => 'main'
                ]
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            return [
                'success' => true,
                'result' => $result
            ];
        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
            $errorData = json_decode($e->getResponse()->getBody()->getContents(), true);

            return [
                'success' => false,
                'error' => $errorData['message'] ?? $this->getErrorMessage($statusCode),
                'status_code' => $statusCode
            ];
        }
    }

    /**
     * 触发GitHub Actions工作流
     */
    public function triggerWorkflow(string $token, string $username, string $repoName, string $workflowFile, array $inputs): array
    {
        try {
            $response = $this->httpClient->post("https://api.github.com/repos/{$username}/{$repoName}/actions/workflows/{$workflowFile}/dispatches", [
                'headers' => [
                    'Authorization' => 'token ' . $token,
                    'Content-Type' => 'application/json'
                ],
                'json' => [
                    'ref' => 'main',
                    'inputs' => $inputs
                ]
            ]);

            return [
                'success' => true,
                'status_code' => $response->getStatusCode()
            ];
        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
            $errorData = json_decode($e->getResponse()->getBody()->getContents(), true);

            return [
                'success' => false,
                'error' => $errorData['message'] ?? $this->getErrorMessage($statusCode),
                'status_code' => $statusCode
            ];
        }
    }

    /**
     * 获取最优的GitHub账号
     */
    public function getBestAccount(): ?array
    {
        $accounts = $this->githubAccountModel->getAllAccounts();

        if (empty($accounts)) {
            return null;
        }

        // 更新所有账号的剩余时间
        foreach ($accounts as $account) {
            $token = $this->githubAccountModel->getAccountToken($account['_id']);
            if ($token) {
                $remainingMinutes = $this->getRemainingMinutes($token);
                $this->githubAccountModel->updateRemainingMinutes($account['_id'], $remainingMinutes);
            }
        }

        // 重新获取排序后的账号
        $accounts = $this->githubAccountModel->getAllAccounts();

        // 返回剩余时间最多的账号
        return !empty($accounts) ? $accounts[0] : null;
    }

    /**
     * 更新所有GitHub账号的剩余时间
     */
    public function updateAllAccountMinutes(): void
    {
        $accounts = $this->githubAccountModel->getAllAccounts();

        foreach ($accounts as $account) {
            $token = $this->githubAccountModel->getAccountToken($account['_id']);
            if ($token) {
                $remainingMinutes = $this->getRemainingMinutes($token);
                $this->githubAccountModel->updateRemainingMinutes($account['_id'], $remainingMinutes);
            }
        }
    }

    /**
     * 获取错误信息
     */
    private function getErrorMessage(int $statusCode): string
    {
        switch ($statusCode) {
            case 401:
                return 'Token无效或已过期';
            case 403:
                return 'Token权限不足';
            case 404:
                return '资源不存在';
            case 422:
                return '请求参数错误';
            case 429:
                return '请求频率超限';
            default:
                return 'GitHub API请求失败';
        }
    }

    /**
     * 生成工作流文件名
     */
    public function generateWorkflowFileName(): string
    {
        return 'upload-testflight.yml';
    }

    /**
     * 生成Repository名称
     */
    public function generateRepoName(): string
    {
        return 'ios-uploader-' . uniqid();
    }

    /**
     * 创建上传工作流
     */
    public function createUploadWorkflow(array $githubAccount, array $config)
    {
        $repoName = $this->getOrCreateUploadRepo($githubAccount);
        $workflowFile = $this->generateWorkflowFile($config);

        // 创建工作流文件
        $this->createWorkflowFile($githubAccount, $repoName, $workflowFile);

        // 设置仓库密钥
        $this->setupRepositorySecrets($githubAccount, $repoName, $config);

        // 触发工作流
        $workflowId = $this->triggerUploadWorkflow($githubAccount, $repoName, $config);

        return [
            'repo_name' => $repoName,
            'workflow_id' => $workflowId,
            'github_url' => "https://github.com/{$githubAccount['username']}/{$repoName}/actions"
        ];
    }

    /**
     * 获取或创建上传仓库
     */
    private function getOrCreateUploadRepo(array $githubAccount)
    {
        $repoName = 'ios-upload-' . date('Y-m');

        try {
            // 检查仓库是否存在
            $response = $this->httpClient->get("https://api.github.com/repos/{$githubAccount['username']}/{$repoName}", [
                'headers' => [
                    'Authorization' => 'token ' . $githubAccount['token']
                ]
            ]);

            echo "仓库已存在: {$githubAccount['username']}/{$repoName}\n";
        } catch (RequestException $e) {
            if ($e->getResponse() && $e->getResponse()->getStatusCode() === 404) {
                // 创建新仓库
                $this->createUploadRepository($githubAccount, $repoName);
            } else {
                throw $e;
            }
        }

        return $repoName;
    }

    /**
     * 创建上传仓库
     */
    private function createUploadRepository(array $githubAccount, string $repoName)
    {
        $data = [
            'name' => $repoName,
            'description' => 'iOS应用自动上传到TestFlight',
            'private' => true,
            'auto_init' => true
        ];

        try {
            $response = $this->httpClient->post('https://api.github.com/user/repos', [
                'headers' => [
                    'Authorization' => 'token ' . $githubAccount['token']
                ],
                'json' => $data
            ]);

            echo "创建GitHub仓库: {$githubAccount['username']}/{$repoName}\n";
        } catch (RequestException $e) {
            throw new \Exception('创建GitHub仓库失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成工作流文件内容
     */
    private function generateWorkflowFile(array $config)
    {
        $uploadId = $config['upload_id'];
        $callbackUrl = 'https://api.ios.xxyx.cn/api/upload/workflow-callback';
        $downloadUrl = $config['download_url'] ?? '';

        $authSteps = '';
        $uploadSteps = '';

        if ($config['auth_method'] === 'api_key') {
            $authSteps = '
    - name: Setup App Store Connect API Key
      run: |
        mkdir -p ~/private_keys
        echo "${{ secrets.API_KEY_CONTENT }}" > ~/private_keys/AuthKey_${{ secrets.API_KEY_ID }}.p8
        chmod 600 ~/private_keys/AuthKey_${{ secrets.API_KEY_ID }}.p8
        echo "API_KEY_ID=${{ secrets.API_KEY_ID }}" >> $GITHUB_ENV
        echo "ISSUER_ID=${{ secrets.ISSUER_ID }}" >> $GITHUB_ENV';

            $uploadSteps = '
    - name: Upload to TestFlight (API Key)
      run: |
        echo "开始上传到TestFlight (使用API Key)"

        # 上报开始状态
        curl -s -X POST ' . $callbackUrl . ' \\
          -H "Content-Type: application/json" \\
          -d \'{"upload_id": "' . $uploadId . '", "status": "progress", "step": "step-testflight", "progress": 80, "message": "开始上传到TestFlight..."}\'

        # 验证应用
        echo "验证应用..."
        if xcrun altool --validate-app \\
          -f "${IPA_FILE}" \\
          -t ios \\
          --apiKey "$API_KEY_ID" \\
          --apiIssuer "$ISSUER_ID" \\
          --verbose; then
          echo "应用验证通过"
        else
          echo "应用验证失败"
          curl -s -X POST ' . $callbackUrl . ' \\
            -H "Content-Type: application/json" \\
            -d \'{"upload_id": "' . $uploadId . '", "status": "failed", "error": "应用验证失败"}\'
          exit 1
        fi

        # 上传应用
        echo "上传应用到TestFlight..."
        curl -s -X POST ' . $callbackUrl . ' \\
          -H "Content-Type: application/json" \\
          -d \'{"upload_id": "' . $uploadId . '", "status": "progress", "step": "step-testflight", "progress": 90, "message": "正在上传到TestFlight..."}\'

        if xcrun altool --upload-app \\
          -f "${IPA_FILE}" \\
          -t ios \\
          --apiKey "$API_KEY_ID" \\
          --apiIssuer "$ISSUER_ID" \\
          --verbose; then
          echo "上传成功！"
        else
          echo "上传失败"
          curl -s -X POST ' . $callbackUrl . ' \\
            -H "Content-Type: application/json" \\
            -d \'{"upload_id": "' . $uploadId . '", "status": "failed", "error": "上传到TestFlight失败"}\'
          exit 1
        fi';
        } else {
            $authSteps = '
    - name: Setup Apple ID
      run: |
        echo "APPLE_ID=${{ secrets.APPLE_ID }}" >> $GITHUB_ENV
        echo "APP_PASSWORD=${{ secrets.APP_PASSWORD }}" >> $GITHUB_ENV
        echo "✅ Apple ID认证信息已设置"';

            $uploadSteps = '
    - name: Upload to TestFlight (Apple ID)
      run: |
        echo "开始上传到TestFlight (使用Apple ID)"

        # 上报开始状态
        curl -s -X POST ' . $callbackUrl . ' \\
          -H "Content-Type: application/json" \\
          -d \'{"upload_id": "' . $uploadId . '", "status": "progress", "step": "step-testflight", "progress": 80, "message": "开始上传到TestFlight..."}\'

        # 验证认证信息
        echo "验证认证信息..."
        echo "Apple ID: $APPLE_ID"
        echo "专属密码长度: ${#APP_PASSWORD}"

        if [ -z "$APPLE_ID" ] || [ -z "$APP_PASSWORD" ]; then
          echo "❌ 认证信息缺失"
          curl -s -X POST ' . $callbackUrl . ' \\
            -H "Content-Type: application/json" \\
            -d \'{"upload_id": "' . $uploadId . '", "status": "failed", "error": "Apple ID或专属密码未设置"}\'
          exit 1
        fi

        # 上传应用
        echo "上传应用到TestFlight..."
        curl -s -X POST ' . $callbackUrl . ' \\
          -H "Content-Type: application/json" \\
          -d \'{"upload_id": "' . $uploadId . '", "status": "progress", "step": "step-testflight", "progress": 90, "message": "正在上传到TestFlight..."}\'

        # 应用信息已在服务端解析，直接使用传递的参数
        BUNDLE_ID="' . $config['bundle_id'] . '"
        VERSION="' . $config['version'] . '"
        BUILD="' . $config['build'] . '"

        echo "应用信息:"
        echo "Bundle ID: $BUNDLE_ID"
        echo "Version: $VERSION"
        echo "Build: $BUILD"

        # 使用完整的altool命令上传，启用详细日志
        echo "开始上传到App Store Connect..."

        # 执行altool命令并捕获退出码
        xcrun altool --upload-app \\
          -f "${IPA_FILE}" \\
          -t ios \\
          -u "$APPLE_ID" \\
          -p "$APP_PASSWORD" \\
          --bundle-id "$BUNDLE_ID" \\
          --bundle-short-version-string "$VERSION" \\
          --bundle-version "$BUILD" \\
          --verbose 2>&1 | tee upload_output.log

        ALTOOL_EXIT_CODE=${PIPESTATUS[0]}
        UPLOAD_RESULT=$(cat upload_output.log)

        # 检查是否有错误（不仅仅依赖退出码，还要检查日志内容）
        if [ $ALTOOL_EXIT_CODE -eq 0 ] && ! echo "$UPLOAD_RESULT" | grep -q "Error uploading" && ! echo "$UPLOAD_RESULT" | grep -q "*** Error:"; then
          echo "✅ 上传成功！"

          # 查找关键信息
          if echo "$UPLOAD_RESULT" | grep -q "RequestUUID"; then
            REQUEST_UUID=$(echo "$UPLOAD_RESULT" | grep "RequestUUID" | head -1 | sed "s/.*RequestUUID[^:]*: *\\([^,}]*\\).*/\\1/")
            echo "上传请求UUID: $REQUEST_UUID"
          fi

          # 发送成功回调
          curl -s -X POST ' . $callbackUrl . ' \\
            -H "Content-Type: application/json" \\
            -d \'{"upload_id": "' . $uploadId . '", "status": "completed", "step": "step-testflight", "progress": 100, "message": "上传完成！App正在处理中，请在App Store Connect中查看"}\'
        else
          echo "❌ 上传失败"
          echo "altool退出码: $ALTOOL_EXIT_CODE"
          echo "完整错误日志:"
          cat upload_output.log

          # 提取详细的错误信息
          ERROR_DETAILS=$(cat upload_output.log)

          # 分析具体的错误类型
          ERROR_MSG="上传到TestFlight失败"
          ERROR_CODE=""

          if echo "$ERROR_DETAILS" | grep -q "bundle version must be higher"; then
            ERROR_MSG="版本号重复：Bundle Version必须高于之前上传的版本"
            ERROR_CODE="VERSION_EXISTS"
          elif echo "$ERROR_DETAILS" | grep -q "Error uploading"; then
            ERROR_MSG="上传过程中发生错误"
            ERROR_CODE="UPLOAD_ERROR"
          elif echo "$ERROR_DETAILS" | grep -q "authentication failed"; then
            ERROR_MSG="认证失败：请检查Apple ID和专属密码是否正确"
            ERROR_CODE="AUTH_FAILED"
          elif echo "$ERROR_DETAILS" | grep -q "Invalid bundle"; then
            ERROR_MSG="IPA文件无效：请检查应用签名和文件完整性"
            ERROR_CODE="INVALID_BUNDLE"
          elif echo "$ERROR_DETAILS" | grep -q "missing required icon"; then
            ERROR_MSG="缺少必需的应用图标"
            ERROR_CODE="MISSING_ICON"
          elif echo "$ERROR_DETAILS" | grep -q "does not contain a valid CFBundleVersion"; then
            ERROR_MSG="CFBundleVersion格式错误"
            ERROR_CODE="INVALID_VERSION"
          elif echo "$ERROR_DETAILS" | grep -q "Redundant Binary Upload"; then
            ERROR_MSG="重复的二进制文件上传：相同版本已存在"
            ERROR_CODE="DUPLICATE_BINARY"
          elif echo "$ERROR_DETAILS" | grep -q "ENTITY_ERROR.ATTRIBUTE.INVALID.DUPLICATE"; then
            ERROR_MSG="版本号重复：该版本已存在，请增加Bundle Version"
            ERROR_CODE="VERSION_EXISTS"
          fi

          echo "错误类型: $ERROR_CODE"
          echo "错误信息: $ERROR_MSG"

          # 发送失败回调，包含具体错误信息
          curl -s -X POST ' . $callbackUrl . ' \\
            -H "Content-Type: application/json" \\
            -d "{\"upload_id\": \"' . $uploadId . '\", \"status\": \"failed\", \"error\": \"$ERROR_MSG\", \"error_code\": \"$ERROR_CODE\"}"
          exit 1
        fi';
        }

        return "name: Upload to TestFlight

on:
  workflow_dispatch:
    inputs:
      upload_id:
        description: 'Upload ID'
        required: true
        type: string

jobs:
  upload:
    runs-on: macos-latest
    timeout-minutes: 30

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Report Start
      run: |
        curl -s -X POST {$callbackUrl} \\
          -H \"Content-Type: application/json\" \\
          -d '{\"upload_id\": \"{$uploadId}\", \"status\": \"started\", \"step\": \"step-validate\", \"progress\": 10, \"message\": \"工作流已启动\"}'

    - name: Download IPA
      run: |
        echo \"下载IPA文件...\"
        curl -s -X POST {$callbackUrl} \\
          -H \"Content-Type: application/json\" \\
          -d '{\"upload_id\": \"{$uploadId}\", \"status\": \"progress\", \"step\": \"step-workflow\", \"progress\": 30, \"message\": \"下载IPA文件...\"}'

        # 从服务器下载IPA文件（使用临时下载链接）
        DOWNLOAD_URL=\"{$downloadUrl}\"
        echo \"下载URL: \$DOWNLOAD_URL\"

        # 下载IPA文件
        if curl -L -f -o app.ipa \"\$DOWNLOAD_URL\"; then
            echo \"IPA文件下载成功\"
            ls -la app.ipa

            # 检查文件大小
            FILE_SIZE=\$(stat -f%z \"app.ipa\" 2>/dev/null || stat -c%s \"app.ipa\" 2>/dev/null)
            echo \"文件大小: \$FILE_SIZE bytes\"

            if [ \"\$FILE_SIZE\" -lt 1000 ]; then
                echo \"下载的文件太小，可能是错误响应\"
                cat app.ipa
                curl -s -X POST {$callbackUrl} \\
                  -H \"Content-Type: application/json\" \\
                  -d '{\"upload_id\": \"{$uploadId}\", \"status\": \"failed\", \"error\": \"IPA文件太小\"}'
                exit 1
            fi

            echo \"IPA_FILE=app.ipa\" >> \$GITHUB_ENV
        else
            echo \"IPA文件下载失败\"
            curl -s -X POST {$callbackUrl} \\
              -H \"Content-Type: application/json\" \\
              -d '{\"upload_id\": \"{$uploadId}\", \"status\": \"failed\", \"error\": \"IPA文件下载失败\"}'
            exit 1
        fi

{$authSteps}

    - name: Verify IPA File
      run: |
        echo \"验证IPA文件...\"
        curl -s -X POST {$callbackUrl} \\
          -H \"Content-Type: application/json\" \\
          -d '{\"upload_id\": \"{$uploadId}\", \"status\": \"progress\", \"step\": \"step-workflow\", \"progress\": 50, \"message\": \"验证IPA文件...\"}'

        # 检查文件大小
        FILE_SIZE=\$(stat -f%z \"\$IPA_FILE\" 2>/dev/null || stat -c%s \"\$IPA_FILE\" 2>/dev/null)
        echo \"IPA文件大小: \$FILE_SIZE bytes\"

        if [ \"\$FILE_SIZE\" -lt 1000000 ]; then
          echo \"IPA文件太小，可能损坏\"
          curl -s -X POST {$callbackUrl} \\
            -H \"Content-Type: application/json\" \\
            -d '{\"upload_id\": \"{$uploadId}\", \"status\": \"failed\", \"error\": \"IPA文件太小，可能损坏\"}'
          exit 1
        fi

        echo \"IPA文件验证通过\"

{$uploadSteps}

    - name: Report Success
      if: success()
      run: |
        echo \"上传完成！\"
        curl -s -X POST {$callbackUrl} \\
          -H \"Content-Type: application/json\" \\
          -d '{\"upload_id\": \"{$uploadId}\", \"status\": \"completed\", \"step\": \"step-testflight\", \"progress\": 100, \"message\": \"上传完成！\", \"result\": {\"bundle_id\": \"{$config['bundle_id']}\", \"app_name\": \"{$config['app_name']}\", \"version\": \"{$config['version']}\", \"build\": \"{$config['build']}\"}}'

    - name: Report Failure
      if: failure()
      run: |
        echo \"上传失败\"
        curl -s -X POST {$callbackUrl} \\
          -H \"Content-Type: application/json\" \\
          -d '{\"upload_id\": \"{$uploadId}\", \"status\": \"failed\", \"error\": \"工作流执行失败\"}'

    - name: Cleanup
      if: always()
      run: |
        rm -f app.ipa
        rm -rf ~/private_keys
        echo \"清理完成\"
";
    }

    /**
     * 创建工作流文件
     */
    private function createWorkflowFile(array $githubAccount, string $repoName, string $workflowContent)
    {
        $path = '.github/workflows/upload-testflight.yml';

        try {
            // 检查文件是否已存在
            $response = $this->httpClient->get("https://api.github.com/repos/{$githubAccount['username']}/{$repoName}/contents/{$path}", [
                'headers' => [
                    'Authorization' => 'token ' . $githubAccount['token']
                ]
            ]);

            $existingFile = json_decode($response->getBody()->getContents(), true);

            // 文件已存在，更新
            $data = [
                'message' => 'Update TestFlight upload workflow',
                'content' => base64_encode($workflowContent),
                'sha' => $existingFile['sha']
            ];
        } catch (RequestException $e) {
            if ($e->getResponse() && $e->getResponse()->getStatusCode() === 404) {
                // 文件不存在，创建新文件
                $data = [
                    'message' => 'Add TestFlight upload workflow',
                    'content' => base64_encode($workflowContent)
                ];
            } else {
                throw $e;
            }
        }

        try {
            $response = $this->httpClient->put("https://api.github.com/repos/{$githubAccount['username']}/{$repoName}/contents/{$path}", [
                'headers' => [
                    'Authorization' => 'token ' . $githubAccount['token']
                ],
                'json' => $data
            ]);

            echo "创建工作流文件: {$path}\n";
        } catch (RequestException $e) {
            throw new \Exception('创建工作流文件失败: ' . $e->getMessage());
        }
    }

    /**
     * 设置仓库密钥
     */
    private function setupRepositorySecrets(array $githubAccount, string $repoName, array $config)
    {
        $secrets = [];

        if ($config['auth_method'] === 'api_key') {
            $secrets = [
                'API_KEY_ID' => $config['auth_data']['api_key_id'],
                'ISSUER_ID' => $config['auth_data']['issuer_id'],
                'API_KEY_CONTENT' => $config['auth_data']['api_key_content']
            ];
        } else {
            $secrets = [
                'APPLE_ID' => $config['auth_data']['apple_id'],
                'APP_PASSWORD' => $config['auth_data']['app_password']
            ];
        }

        foreach ($secrets as $name => $value) {
            $this->createRepositorySecret($githubAccount, $repoName, $name, $value);
        }
    }

    /**
     * 创建仓库密钥
     */
    private function createRepositorySecret(array $githubAccount, string $repoName, string $name, string $value)
    {
        try {
            // 获取公钥
            $response = $this->httpClient->get("https://api.github.com/repos/{$githubAccount['username']}/{$repoName}/actions/secrets/public-key", [
                'headers' => [
                    'Authorization' => 'token ' . $githubAccount['token']
                ]
            ]);

            $publicKey = json_decode($response->getBody()->getContents(), true);

            // 使用libsodium进行正确的加密
            if (!function_exists('sodium_crypto_box_seal')) {
                throw new \Exception('需要安装libsodium扩展');
            }

            $publicKeyBinary = base64_decode($publicKey['key']);
            $encryptedValue = base64_encode(sodium_crypto_box_seal($value, $publicKeyBinary));

            // 创建密钥
            $data = [
                'encrypted_value' => $encryptedValue,
                'key_id' => $publicKey['key_id']
            ];

            $response = $this->httpClient->put("https://api.github.com/repos/{$githubAccount['username']}/{$repoName}/actions/secrets/{$name}", [
                'headers' => [
                    'Authorization' => 'token ' . $githubAccount['token']
                ],
                'json' => $data
            ]);

            echo "创建仓库密钥: {$name}\n";
        } catch (RequestException $e) {
            echo "创建密钥 {$name} 失败: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 触发上传工作流
     */
    private function triggerUploadWorkflow(array $githubAccount, string $repoName, array $config)
    {
        $data = [
            'ref' => 'main',
            'inputs' => [
                'upload_id' => $config['upload_id']
            ]
        ];

        try {
            $response = $this->httpClient->post("https://api.github.com/repos/{$githubAccount['username']}/{$repoName}/actions/workflows/upload-testflight.yml/dispatches", [
                'headers' => [
                    'Authorization' => 'token ' . $githubAccount['token']
                ],
                'json' => $data
            ]);

            echo "触发GitHub工作流\n";
            return uniqid('workflow_');
        } catch (RequestException $e) {
            throw new \Exception('触发工作流失败: ' . $e->getMessage());
        }
    }
}
