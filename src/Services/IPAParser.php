<?php

namespace App\Services;

use App\Config\AppConfig;
use ZipArchive;

class IPAParser
{
    private AppConfig $config;
    private string $tmpPath;
    private string $iconPath;

    public function __construct()
    {
        $this->config = AppConfig::getInstance();
        $this->tmpPath = $this->config->get('upload_tmp_path');
        $this->iconPath = $this->config->get('icon_storage_path');
    }

    /**
     * 解析IPA文件，提取应用信息
     */
    public function parseIPA(string $ipaPath): array
    {
        if (!file_exists($ipaPath)) {
            throw new \RuntimeException('IPA文件不存在: ' . $ipaPath);
        }

        // 创建临时目录
        $tempDir = $this->tmpPath . '/' . uniqid('ipa_parse_');
        if (!mkdir($tempDir, 0755, true)) {
            throw new \RuntimeException('无法创建临时目录');
        }

        try {
            // 解压IPA文件
            $this->extractIPA($ipaPath, $tempDir);

            // 查找Payload目录
            $payloadDir = $tempDir . '/Payload';
            if (!is_dir($payloadDir)) {
                throw new \RuntimeException('IPA文件格式错误：缺少Payload目录');
            }

            // 查找.app目录
            $appDirs = glob($payloadDir . '/*.app');
            if (empty($appDirs)) {
                throw new \RuntimeException('IPA文件格式错误：缺少.app目录');
            }

            $appDir = $appDirs[0];
            $appName = basename($appDir, '.app');

            // 解析Info.plist
            $infoPlistPath = $appDir . '/Info.plist';
            if (!file_exists($infoPlistPath)) {
                throw new \RuntimeException('缺少Info.plist文件');
            }

            $appInfo = $this->parseInfoPlist($infoPlistPath);

            // 提取图标
            $iconPath = $this->extractIcon($appDir, $appInfo['bundle_id']);

            return [
                'bundle_id' => $appInfo['bundle_id'],
                'app_name' => $appInfo['app_name'],
                'version' => $appInfo['version'],
                'build' => $appInfo['build'],
                'icon_path' => $iconPath,
                'file_size' => filesize($ipaPath)
            ];
        } finally {
            // 清理临时目录
            $this->cleanupTempDir($tempDir);
        }
    }

    /**
     * 解压IPA文件
     */
    private function extractIPA(string $ipaPath, string $extractPath): void
    {
        $zip = new ZipArchive();
        $result = $zip->open($ipaPath);

        if ($result !== ZipArchive::ER_OK) {
            throw new \RuntimeException('无法打开IPA文件，错误代码: ' . $result);
        }

        $zip->extractTo($extractPath);
        $zip->close();
    }

    /**
     * 解析Info.plist文件
     */
    private function parseInfoPlist(string $infoPlistPath): array
    {
        $content = file_get_contents($infoPlistPath);
        if ($content === false) {
            throw new \RuntimeException('无法读取Info.plist文件');
        }

        // 使用plutil命令解析plist文件（macOS/Linux）
        $tempPlist = tempnam(sys_get_temp_dir(), 'plist_');
        file_put_contents($tempPlist, $content);

        try {
            // 获取Bundle ID
            $bundleId = $this->executeCommand("plutil -extract CFBundleIdentifier raw '$tempPlist'");

            // 获取应用名称
            $appName = $this->executeCommand("plutil -extract CFBundleDisplayName raw '$tempPlist'");
            if (empty($appName)) {
                $appName = $this->executeCommand("plutil -extract CFBundleName raw '$tempPlist'");
            }
            if (empty($appName)) {
                $appName = basename(dirname($infoPlistPath), '.app');
            }

            // 获取版本号
            $version = $this->executeCommand("plutil -extract CFBundleShortVersionString raw '$tempPlist'");
            if (empty($version)) {
                $version = '1.0';
            }

            // 获取构建号
            $build = $this->executeCommand("plutil -extract CFBundleVersion raw '$tempPlist'");
            if (empty($build)) {
                $build = '1';
            }

            return [
                'bundle_id' => trim($bundleId),
                'app_name' => trim($appName),
                'version' => trim($version),
                'build' => trim($build)
            ];
        } finally {
            unlink($tempPlist);
        }
    }

    /**
     * 提取应用图标
     */
    private function extractIcon(string $appDir, string $bundleId): ?string
    {
        // 查找图标文件
        $iconFiles = [
            '<EMAIL>',
            'AppIcon60x60.png',
            '<EMAIL>',
            'AppIcon40x40.png',
            '<EMAIL>',
            'AppIcon29x29.png',
            'Icon.png',
            'icon.png'
        ];

        foreach ($iconFiles as $iconFile) {
            $iconPath = $appDir . '/' . $iconFile;
            if (file_exists($iconPath)) {
                // 复制到图标存储目录
                $targetPath = $this->iconPath . '/' . $bundleId . '_' . time() . '.png';
                if (copy($iconPath, $targetPath)) {
                    return basename($targetPath);
                }
            }
        }

        // 如果没有找到图标，尝试从Assets.car中提取
        $assetsPath = $appDir . '/Assets.car';
        if (file_exists($assetsPath)) {
            return $this->extractIconFromAssets($assetsPath, $bundleId);
        }

        return null;
    }

    /**
     * 从Assets.car中提取图标（简化版本）
     */
    private function extractIconFromAssets(string $assetsPath, string $bundleId): ?string
    {
        // 这里可以集成assetutil工具来提取图标
        // 暂时返回null，后续可以完善
        return null;
    }

    /**
     * 执行系统命令
     */
    private function executeCommand(string $command): string
    {
        $output = shell_exec($command . ' 2>/dev/null');
        return $output ? trim($output) : '';
    }

    /**
     * 清理临时目录
     */
    private function cleanupTempDir(string $tempDir): void
    {
        if (is_dir($tempDir)) {
            $this->removeDirectory($tempDir);
        }
    }

    /**
     * 递归删除目录
     */
    private function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->removeDirectory($path);
            } else {
                unlink($path);
            }
        }
        rmdir($dir);
    }

    /**
     * 验证IPA文件格式
     */
    public function validateIPA(string $ipaPath): bool
    {
        if (!file_exists($ipaPath)) {
            return false;
        }

        $zip = new ZipArchive();
        $result = $zip->open($ipaPath);

        if ($result !== ZipArchive::ER_OK) {
            return false;
        }

        // 检查是否包含Payload目录
        $hasPayload = false;
        for ($i = 0; $i < $zip->numFiles; $i++) {
            $filename = $zip->getNameIndex($i);
            if (strpos($filename, 'Payload/') === 0) {
                $hasPayload = true;
                break;
            }
        }

        $zip->close();
        return $hasPayload;
    }

    /**
     * 获取IPA文件大小（MB）
     */
    public function getFileSizeMB(string $ipaPath): float
    {
        if (!file_exists($ipaPath)) {
            return 0;
        }

        return filesize($ipaPath) / (1024 * 1024);
    }
}
