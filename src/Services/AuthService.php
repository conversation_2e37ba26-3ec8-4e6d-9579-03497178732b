<?php

namespace App\Services;

use App\Config\AppConfig;
use App\Models\User;
use App\Models\ActivationCode;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

class AuthService
{
    private AppConfig $config;
    private User $userModel;
    private ActivationCode $activationCodeModel;

    public function __construct()
    {
        $this->config = AppConfig::getInstance();
        $this->userModel = new User();
        $this->activationCodeModel = new ActivationCode();
    }

    /**
     * 用户登录
     */
    public function login(string $usernameOrEmail, string $password): array
    {
        // 查找用户（支持用户名或邮箱）
        $user = null;

        // 先尝试按邮箱查找
        if (filter_var($usernameOrEmail, FILTER_VALIDATE_EMAIL)) {
            $user = $this->userModel->findByEmail($usernameOrEmail);
        }

        // 如果按邮箱没找到，再按用户名查找
        if (!$user) {
            $user = $this->userModel->findByUsername($usernameOrEmail);
        }

        if (!$user) {
            return [
                'success' => false,
                'error' => '用户不存在'
            ];
        }

        // 验证密码
        if (!$this->userModel->verifyPassword($password, $user['password'])) {
            return [
                'success' => false,
                'error' => '密码错误'
            ];
        }

        // 检查用户状态
        if ($user['status'] !== 'active') {
            return [
                'success' => false,
                'error' => '账号未激活，请使用激活码激活'
            ];
        }

        // 更新最后登录时间
        $this->userModel->updateLastLogin($user['_id']);

        // 生成JWT token
        $token = $this->generateJWT($user);

        return [
            'success' => true,
            'user' => $this->sanitizeUserData($user),
            'token' => $token
        ];
    }

    /**
     * 激活账号
     */
    public function activate(string $username, string $activationCode): array
    {
        // 查找用户
        $user = $this->userModel->findByUsername($username);
        if (!$user) {
            return [
                'success' => false,
                'error' => '用户不存在'
            ];
        }

        // 检查用户状态
        if ($user['status'] === 'active') {
            return [
                'success' => false,
                'error' => '账号已经激活'
            ];
        }

        // 查找激活码
        $code = $this->activationCodeModel->findByCode($activationCode);
        if (!$code) {
            return [
                'success' => false,
                'error' => '激活码不存在'
            ];
        }

        // 检查激活码状态
        if ($this->activationCodeModel->isUsed($code)) {
            return [
                'success' => false,
                'error' => '激活码已被使用'
            ];
        }

        if ($this->activationCodeModel->isExpired($code)) {
            return [
                'success' => false,
                'error' => '激活码已过期'
            ];
        }

        // 激活用户
        if (!$this->userModel->activate($user['_id'])) {
            return [
                'success' => false,
                'error' => '激活失败'
            ];
        }

        // 标记激活码为已使用
        $this->activationCodeModel->useCode($code['_id'], $user['_id']);

        return [
            'success' => true,
            'message' => '账号激活成功',
            'user' => $this->sanitizeUserData($user)
        ];
    }

    /**
     * 验证JWT token
     */
    public function verifyToken(string $token): ?array
    {
        try {
            $decoded = JWT::decode($token, new Key($this->config->get('jwt_secret'), 'HS256'));
            $payload = (array) $decoded;

            // 检查token是否过期
            if (isset($payload['exp']) && $payload['exp'] < time()) {
                return null;
            }

            // 获取用户信息
            $user = $this->userModel->findById($payload['user_id']);
            if (!$user || $user['status'] !== 'active') {
                return null;
            }

            return $this->sanitizeUserData($user);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 生成JWT token
     */
    private function generateJWT(array $user): string
    {
        $payload = [
            'user_id' => $user['_id'],
            'email' => $user['email'],
            'role' => $user['role'],
            'iat' => time(),
            'exp' => time() + (24 * 60 * 60), // 24小时过期
            'iss' => 'ios-tool-app'
        ];

        return JWT::encode($payload, $this->config->get('jwt_secret'), 'HS256');
    }

    /**
     * 清理用户数据（移除敏感信息）
     */
    private function sanitizeUserData(array $user): array
    {
        unset($user['password']);
        return $user;
    }

    /**
     * 验证API Key格式
     */
    public function validateAPIKey(string $apiKeyId, string $issuerId, string $apiKeyContent): array
    {
        $errors = [];
        $warnings = [];

        // 验证API Key ID
        if (empty($apiKeyId)) {
            $errors[] = 'API Key ID 不能为空';
        } elseif (!preg_match('/^[A-Z0-9]{10}$/', $apiKeyId)) {
            $errors[] = 'API Key ID 格式错误，应该是10个大写字母和数字';
        }

        // 验证Issuer ID
        if (empty($issuerId)) {
            $errors[] = 'Issuer ID 不能为空';
        } elseif (!preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $issuerId)) {
            $errors[] = 'Issuer ID 格式错误，应该是 UUID 格式';
        }

        // 验证私钥内容
        if (empty($apiKeyContent)) {
            $errors[] = 'API Key 私钥内容不能为空';
        } else {
            $lines = explode("\n", trim($apiKeyContent));

            // 检查开始和结束标记
            if (!str_contains($lines[0], '-----BEGIN PRIVATE KEY-----')) {
                $errors[] = '私钥缺少开始标记 "-----BEGIN PRIVATE KEY-----"';
            }
            if (!str_contains($lines[count($lines) - 1], '-----END PRIVATE KEY-----')) {
                $errors[] = '私钥缺少结束标记 "-----END PRIVATE KEY-----"';
            }

            // 检查私钥长度
            if (count($lines) < 3) {
                $errors[] = '私钥内容太短，可能不完整';
            }

            // 检查base64内容
            $keyContent = implode('', array_slice($lines, 1, -1));
            if (strlen($keyContent) < 50) {
                $warnings[] = '私钥内容较短，请确认是否完整';
            }

            // 检查是否包含无效字符
            if (!preg_match('/^[A-Za-z0-9+\/=\s]*$/', $keyContent)) {
                $errors[] = '私钥内容包含无效字符';
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings
        ];
    }

    /**
     * 验证Apple ID格式
     */
    public function validateAppleID(string $appleId, string $appPassword): array
    {
        $errors = [];
        $warnings = [];

        // 验证Apple ID格式
        if (empty($appleId)) {
            $errors[] = 'Apple ID 不能为空';
        } elseif (!filter_var($appleId, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Apple ID 格式错误，应该是有效的邮箱地址';
        }

        // 验证专属密码格式
        if (empty($appPassword)) {
            $errors[] = '专属密码不能为空';
        } elseif (!preg_match('/^[a-z]{4}-[a-z]{4}-[a-z]{4}-[a-z]{4}$/', $appPassword)) {
            $errors[] = '专属密码格式错误，应该是 xxxx-xxxx-xxxx-xxxx 格式（16个小写字母）';
        } else {
            // 检查是否包含数字或大写字母
            if (preg_match('/[A-Z0-9]/', $appPassword)) {
                $warnings[] = '专属密码通常只包含小写字母，请确认格式正确';
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings
        ];
    }

    /**
     * 检查用户权限
     */
    public function hasPermission(array $user, string $permission): bool
    {
        // 管理员拥有所有权限
        if ($user['role'] === 'admin') {
            return true;
        }

        // 普通用户权限检查
        switch ($permission) {
            case 'upload_ipa':
                return $user['role'] === 'user';
            case 'view_own_records':
                return $user['role'] === 'user';
            default:
                return false;
        }
    }

    /**
     * 刷新token
     */
    public function refreshToken(string $token): ?string
    {
        $user = $this->verifyToken($token);
        if (!$user) {
            return null;
        }

        return $this->generateJWT($user);
    }
}
