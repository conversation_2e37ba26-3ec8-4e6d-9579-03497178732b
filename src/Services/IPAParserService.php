<?php

namespace App\Services;

class IPAParserService
{
    /**
     * 解析IPA文件信息
     */
    public function parseIPA(string $ipaPath): array
    {
        $tempDir = sys_get_temp_dir() . '/ipa_extract_' . uniqid();
        
        try {
            // 创建临时目录
            if (!mkdir($tempDir, 0755, true)) {
                throw new \Exception('无法创建临时目录');
            }

            // 解压IPA文件
            $zip = new \ZipArchive();
            if ($zip->open($ipaPath) !== TRUE) {
                throw new \Exception('无法打开IPA文件');
            }

            $zip->extractTo($tempDir);
            $zip->close();

            // 查找Payload目录
            $payloadDir = $tempDir . '/Payload';
            if (!is_dir($payloadDir)) {
                throw new \Exception('IPA文件格式错误：找不到Payload目录');
            }

            // 查找.app目录
            $appDirs = glob($payloadDir . '/*.app');
            if (empty($appDirs)) {
                throw new \Exception('IPA文件格式错误：找不到.app目录');
            }

            $appDir = $appDirs[0];
            $infoPlistPath = $appDir . '/Info.plist';

            if (!file_exists($infoPlistPath)) {
                throw new \Exception('找不到Info.plist文件');
            }

            // 解析Info.plist
            $plistContent = file_get_contents($infoPlistPath);
            $plist = $this->parsePlist($plistContent);

            // 提取应用信息
            $info = [
                'app_name' => $plist['CFBundleDisplayName'] ?? $plist['CFBundleName'] ?? 'Unknown',
                'bundle_id' => $plist['CFBundleIdentifier'] ?? 'Unknown',
                'version' => $plist['CFBundleShortVersionString'] ?? 'Unknown',
                'build' => $plist['CFBundleVersion'] ?? 'Unknown',
                'icon_path' => null
            ];

            // 尝试提取应用图标
            $iconPath = $this->extractAppIcon($appDir, $plist);
            if ($iconPath) {
                $info['icon_path'] = $iconPath;
            }

            return $info;

        } finally {
            // 清理临时目录
            $this->removeDirectory($tempDir);
        }
    }

    /**
     * 解析plist文件
     */
    private function parsePlist(string $content): array
    {
        $result = [];
        
        // 检查是否是二进制plist格式
        if (substr($content, 0, 8) === 'bplist00') {
            return $this->parseBinaryPlist($content);
        }
        
        // 尝试使用SimpleXML解析XML格式的plist
        try {
            $xml = simplexml_load_string($content);
            if ($xml !== false) {
                $result = $this->parseSimpleXMLPlist($xml);
                if (!empty($result)) {
                    return $result;
                }
            }
        } catch (\Exception $e) {
            // 继续尝试其他方法
        }
        
        // 备用方案：正则表达式解析XML格式
        preg_match_all('/<key>(.*?)<\/key>\s*<string>(.*?)<\/string>/s', $content, $matches, PREG_SET_ORDER);
        
        foreach ($matches as $match) {
            $key = trim($match[1]);
            $value = trim($match[2]);
            $result[$key] = $value;
        }

        return $result;
    }

    /**
     * 解析二进制plist文件
     */
    private function parseBinaryPlist(string $content): array
    {
        $result = [];
        
        try {
            // 创建临时文件
            $tempFile = tempnam(sys_get_temp_dir(), 'bplist_');
            file_put_contents($tempFile, $content);
            
            // 尝试使用plutil转换为XML格式
            $xmlFile = $tempFile . '.xml';
            $command = "plutil -convert xml1 -o " . escapeshellarg($xmlFile) . " " . escapeshellarg($tempFile) . " 2>/dev/null";
            exec($command, $output, $returnCode);
            
            if ($returnCode === 0 && file_exists($xmlFile)) {
                $xmlContent = file_get_contents($xmlFile);
                
                // 解析转换后的XML
                $xml = simplexml_load_string($xmlContent);
                if ($xml !== false) {
                    $result = $this->parseSimpleXMLPlist($xml);
                }
                
                unlink($xmlFile);
            } else {
                // 如果plutil不可用，尝试手动解析关键字段
                $result = $this->parseBasicBinaryPlist($content);
            }
            
            unlink($tempFile);
            
        } catch (\Exception $e) {
            // 解析失败，返回空数组
        }
        
        return $result;
    }

    /**
     * 使用SimpleXML解析plist
     */
    private function parseSimpleXMLPlist(\SimpleXMLElement $xml): array
    {
        $result = [];
        
        // 查找dict元素
        $dict = $xml->dict ?? null;
        if (!$dict) {
            return $result;
        }
        
        $children = $dict->children();
        $key = null;
        
        foreach ($children as $child) {
            if ($child->getName() === 'key') {
                $key = (string)$child;
            } elseif ($key && $child->getName() === 'string') {
                $result[$key] = (string)$child;
                $key = null;
            }
        }
        
        return $result;
    }

    /**
     * 基础的二进制plist解析（提取常见字段）
     */
    private function parseBasicBinaryPlist(string $content): array
    {
        $result = [];
        
        // 提取所有可能的字符串
        preg_match_all('/[\x20-\x7E\x80-\xFF]{3,50}/', $content, $matches);
        $strings = array_unique($matches[0]);
        
        // 查找应用信息
        foreach ($strings as $string) {
            $clean = trim($string);
            if (strlen($clean) >= 3 && strlen($clean) <= 50) {
                // 跳过明显的字段名
                if (strpos($clean, 'CFBundle') !== false || 
                    strpos($clean, 'UI') === 0 || 
                    strpos($clean, 'DT') === 0 ||
                    strpos($clean, 'LS') === 0 ||
                    strpos($clean, 'NS') === 0) {
                    continue;
                }
                
                // 跳过包含下划线或特殊字符的字符串
                if (strpos($clean, '_') !== false || 
                    strpos($clean, '[') !== false ||
                    strpos($clean, ']') !== false) {
                    continue;
                }
                
                // 检查是否是Bundle ID格式
                if (preg_match('/^[a-zA-Z0-9.-]+\.[a-zA-Z0-9.-]+$/', $clean)) {
                    $result['CFBundleIdentifier'] = $clean;
                    continue;
                }
                
                // 检查是否是版本号格式
                if (preg_match('/^\d+(\.\d+)*$/', $clean)) {
                    if (!isset($result['CFBundleShortVersionString'])) {
                        $result['CFBundleShortVersionString'] = $clean;
                    } elseif (!isset($result['CFBundleVersion'])) {
                        $result['CFBundleVersion'] = $clean;
                    }
                    continue;
                }
                
                // 检查是否是应用名称（包含字母，可能包含中文）
                if (preg_match('/[\p{L}]/u', $clean) && !isset($result['CFBundleDisplayName'])) {
                    $result['CFBundleDisplayName'] = $clean;
                    continue;
                }
            }
        }
        
        return $result;
    }

    /**
     * 提取应用图标
     */
    private function extractAppIcon(string $appDir, array $plist): ?string
    {
        // 常见的图标文件名
        $iconNames = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            'Icon.png'
        ];

        foreach ($iconNames as $iconName) {
            $iconPath = $appDir . '/' . $iconName;
            if (file_exists($iconPath)) {
                return $iconPath;
            }
        }

        return null;
    }

    /**
     * 递归删除目录
     */
    private function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->removeDirectory($path);
            } else {
                unlink($path);
            }
        }
        rmdir($dir);
    }
}
