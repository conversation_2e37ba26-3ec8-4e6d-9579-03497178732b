<?php

namespace App\Services;

use App\Config\AppConfig;

class WorkflowGenerator
{
  private AppConfig $config;

  public function __construct()
  {
    $this->config = AppConfig::getInstance();
  }

  /**
   * 生成API Key方式的工作流
   */
  public function generateAPIKeyWorkflow(array $data): string
  {
    $apiKeyId = $data['api_key_id'];
    $issuerId = $data['issuer_id'];
    $apiKeyContent = $data['api_key_content'];
    $recordId = $data['record_id'];
    $callbackUrl = $data['callback_url'];

    return <<<YAML
name: Upload to TestFlight

# 使用官方推荐的 altool 方法
# 生成时间: {$this->getCurrentTime()}

on:
  workflow_dispatch:
    inputs:
      record_id:
        description: 'Upload record ID'
        required: true
        default: '{$recordId}'
      callback_url:
        description: 'Callback URL'
        required: true
        default: '{$callbackUrl}'

jobs:
  upload:
    runs-on: macos-latest
    timeout-minutes: 20
    env:
      API_KEY_ID: "{$apiKeyId}"
      ISSUER_ID: "{$issuerId}"
      API_KEY_CONTENT: |
{$this->generateYAMLMultilineString($apiKeyContent)}

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Download IPA file
      run: |
        echo "📥 Downloading IPA file..."
        curl -L "https://download.ios.xxyx.cn/ipa/{$recordId}" -o app.ipa
        
        if [ ! -f "app.ipa" ]; then
          echo "❌ Failed to download IPA file"
          exit 1
        fi
        
        echo "✅ IPA file downloaded successfully"
        ls -la app.ipa

    - name: Create API Key file
      run: |
        echo "🔑 Creating API Key file..."
        mkdir -p ~/private_keys
        echo "$API_KEY_CONTENT" > ~/private_keys/AuthKey_$API_KEY_ID.p8
        chmod 600 ~/private_keys/AuthKey_$API_KEY_ID.p8
        echo "✅ API Key file created"

    - name: Validate App
      id: validate
      continue-on-error: true
      run: |
        echo "🔍 Validating app before upload..."

        if xcrun altool --validate-app \\
          -f "app.ipa" \\
          -t ios \\
          --apiKey "$API_KEY_ID" \\
          --apiIssuer "$ISSUER_ID" \\
          --verbose 2>&1 | tee validation_output.log; then
          echo "✅ Validation passed"
          echo "validation_result=success" >> \$GITHUB_OUTPUT
        else
          echo "❌ Validation failed"
          echo "validation_result=failed" >> \$GITHUB_OUTPUT
          
          if grep -q "bundle version must be higher" validation_output.log; then
            echo "error_type=version_exists" >> \$GITHUB_OUTPUT
          elif grep -q "ENTITY_ERROR.ATTRIBUTE.INVALID.DUPLICATE" validation_output.log; then
            echo "error_type=duplicate_version" >> \$GITHUB_OUTPUT
          elif grep -q "authentication" validation_output.log; then
            echo "error_type=auth_failed" >> \$GITHUB_OUTPUT
          else
            echo "error_type=unknown" >> \$GITHUB_OUTPUT
          fi
        fi

    - name: Upload to TestFlight
      if: steps.validate.outputs.validation_result != 'failed'
      run: |
        echo "🚀 Uploading to TestFlight..."
        
        if xcrun altool --upload-package \\
          "app.ipa" \\
          -t ios \\
          --apiKey "$API_KEY_ID" \\
          --apiIssuer "$ISSUER_ID" \\
          --verbose 2>&1 | tee upload_output.log; then
          echo "✅ Upload succeeded!"
          echo "🎉 Your app is now processing in App Store Connect"
          
          # 报告成功
          curl -X POST "{$callbackUrl}" \\
            -H "Content-Type: application/json" \\
            -d '{"status":"success","result":{"message":"Upload completed successfully"}}'
        else
          echo "❌ Upload failed"
          
          # 分析错误
          if grep -q "bundle version must be higher" upload_output.log; then
            ERROR_TYPE="version_exists"
            ERROR_MSG="Bundle version already exists"
          elif grep -q "ENTITY_ERROR.ATTRIBUTE.INVALID.DUPLICATE" upload_output.log; then
            ERROR_TYPE="duplicate_version"
            ERROR_MSG="Duplicate version detected"
          elif grep -q "authentication" upload_output.log; then
            ERROR_TYPE="auth_failed"
            ERROR_MSG="Authentication failed"
          else
            ERROR_TYPE="unknown"
            ERROR_MSG="Unknown upload error"
          fi
          
          # 报告失败
          curl -X POST "{$callbackUrl}" \\
            -H "Content-Type: application/json" \\
            -d '{"status":"failed","error":{"type":"'$ERROR_TYPE'","message":"'$ERROR_MSG'"}}'
          
          exit 1
        fi

    - name: Report Validation Failure
      if: steps.validate.outputs.validation_result == 'failed'
      run: |
        ERROR_TYPE="\${{ steps.validate.outputs.error_type }}"
        
        case "$ERROR_TYPE" in
          "version_exists"|"duplicate_version")
            ERROR_MSG="Version already exists in App Store"
            ;;
          "auth_failed")
            ERROR_MSG="Authentication failed"
            ;;
          *)
            ERROR_MSG="Validation failed"
            ;;
        esac
        
        curl -X POST "{$callbackUrl}" \\
          -H "Content-Type: application/json" \\
          -d '{"status":"failed","error":{"type":"'$ERROR_TYPE'","message":"'$ERROR_MSG'"}}'

    - name: Clean up
      if: always()
      run: |
        rm -rf ~/private_keys
        echo "🧹 Cleanup completed"
YAML;
  }

  /**
   * 生成Apple ID方式的工作流
   */
  public function generateAppleIDWorkflow(array $data): string
  {
    $appleId = $data['apple_id'];
    $appPassword = $data['app_password'];
    $recordId = $data['record_id'];
    $callbackUrl = $data['callback_url'];

    return <<<YAML
name: Upload to TestFlight

# 使用Apple ID认证方式
# 生成时间: {$this->getCurrentTime()}

on:
  workflow_dispatch:
    inputs:
      record_id:
        description: 'Upload record ID'
        required: true
        default: '{$recordId}'
      callback_url:
        description: 'Callback URL'
        required: true
        default: '{$callbackUrl}'

jobs:
  upload:
    runs-on: macos-latest
    timeout-minutes: 30
    env:
      APPLE_ID: "{$appleId}"
      APP_PASSWORD: "{$appPassword}"

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Download IPA file
      run: |
        echo "📥 Downloading IPA file..."
        curl -L "https://download.ios.xxyx.cn/ipa/{$recordId}" -o app.ipa
        
        if [ ! -f "app.ipa" ]; then
          echo "❌ Failed to download IPA file"
          exit 1
        fi
        
        echo "✅ IPA file downloaded successfully"
        ls -la app.ipa

    - name: Validate App Specific Password Format
      run: |
        echo "🔍 Validating App Specific Password format..."
        
        if [[ ! "$APP_PASSWORD" =~ ^[a-z]{4}-[a-z]{4}-[a-z]{4}-[a-z]{4}$ ]]; then
          echo "❌ 专属密码格式错误！"
          echo "正确格式: xxxx-xxxx-xxxx-xxxx (16个小写字母)"
          exit 1
        fi
        
        echo "✅ 专属密码格式正确"

    - name: Upload using Apple ID
      run: |
        echo "🍎 Uploading using Apple ID..."
        
        if xcrun altool --upload-app \\
          -f "app.ipa" \\
          -t ios \\
          -u "$APPLE_ID" \\
          -p "$APP_PASSWORD" \\
          --verbose 2>&1 | tee upload_output.log; then
          echo "✅ Upload succeeded!"
          
          # 报告成功
          curl -X POST "{$callbackUrl}" \\
            -H "Content-Type: application/json" \\
            -d '{"status":"success","result":{"message":"Upload completed successfully"}}'
        else
          echo "❌ Upload failed"
          
          # 分析错误
          if grep -q "bundle version must be higher" upload_output.log; then
            ERROR_TYPE="version_exists"
            ERROR_MSG="Bundle version already exists"
          elif grep -q "ENTITY_ERROR.ATTRIBUTE.INVALID.DUPLICATE" upload_output.log; then
            ERROR_TYPE="duplicate_version"
            ERROR_MSG="Duplicate version detected"
          elif grep -q "authentication" upload_output.log; then
            ERROR_TYPE="auth_failed"
            ERROR_MSG="Authentication failed"
          else
            ERROR_TYPE="unknown"
            ERROR_MSG="Unknown upload error"
          fi
          
          # 报告失败
          curl -X POST "{$callbackUrl}" \\
            -H "Content-Type: application/json" \\
            -d '{"status":"failed","error":{"type":"'$ERROR_TYPE'","message":"'$ERROR_MSG'"}}'
          
          exit 1
        fi

    - name: Clean up
      if: always()
      run: |
        echo "🧹 Cleanup completed"
YAML;
  }

  /**
   * 生成YAML多行字符串
   */
  private function generateYAMLMultilineString(string $content, int $baseIndent = 8): string
  {
    $lines = explode("\n", $content);
    $indentStr = str_repeat(' ', $baseIndent);

    $result = [];
    foreach ($lines as $line) {
      $result[] = $indentStr . $line;
    }

    return implode("\n", $result);
  }

  /**
   * 获取当前时间
   */
  private function getCurrentTime(): string
  {
    return date('Y-m-d H:i:s');
  }

  /**
   * 生成高级API Key工作流（支持更多配置选项）
   */
  public function generateAdvancedAPIKeyWorkflow(array $data): string
  {
    $uploadId = $data['upload_id'];
    $callbackUrl = $data['callback_url'];
    $downloadUrl = $data['download_url'] ?? '';
    $bundleId = $data['bundle_id'] ?? '';
    $appName = $data['app_name'] ?? '';
    $version = $data['version'] ?? '';
    $build = $data['build'] ?? '';
    $releaseNotes = $data['release_notes'] ?? '';

    return "name: Upload to TestFlight

# 高级工作流 - API Key认证
# 生成时间: {$this->getCurrentTime()}

on:
  workflow_dispatch:
    inputs:
      upload_id:
        description: 'Upload ID'
        required: true
        type: string

jobs:
  upload:
    runs-on: macos-latest
    timeout-minutes: 30

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Report Start
      run: |
        curl -s -X POST {$callbackUrl} \\
          -H \"Content-Type: application/json\" \\
          -d '{\"upload_id\": \"{$uploadId}\", \"status\": \"started\", \"step\": \"step-validate\", \"progress\": 10, \"message\": \"工作流已启动\"}'

    - name: Download IPA
      run: |
        echo \"下载IPA文件...\"
        curl -s -X POST {$callbackUrl} \\
          -H \"Content-Type: application/json\" \\
          -d '{\"upload_id\": \"{$uploadId}\", \"status\": \"progress\", \"step\": \"step-workflow\", \"progress\": 30, \"message\": \"下载IPA文件...\"}'

        DOWNLOAD_URL=\"{$downloadUrl}\"
        echo \"下载URL: \$DOWNLOAD_URL\"

        if curl -L -f -o app.ipa \"\$DOWNLOAD_URL\"; then
            echo \"IPA文件下载成功\"
            ls -la app.ipa
            FILE_SIZE=\$(stat -f%z \"app.ipa\" 2>/dev/null || stat -c%s \"app.ipa\" 2>/dev/null)
            echo \"文件大小: \$FILE_SIZE bytes\"
            if [ \"\$FILE_SIZE\" -lt 1000 ]; then
                echo \"下载的文件太小，可能是错误响应\"
                curl -s -X POST {$callbackUrl} \\
                  -H \"Content-Type: application/json\" \\
                  -d '{\"upload_id\": \"{$uploadId}\", \"status\": \"failed\", \"error\": \"IPA文件太小\"}'
                exit 1
            fi
            echo \"IPA_FILE=app.ipa\" >> \$GITHUB_ENV
        else
            echo \"IPA文件下载失败\"
            curl -s -X POST {$callbackUrl} \\
              -H \"Content-Type: application/json\" \\
              -d '{\"upload_id\": \"{$uploadId}\", \"status\": \"failed\", \"error\": \"IPA文件下载失败\"}'
            exit 1
        fi

    - name: Setup App Store Connect API Key
      run: |
        mkdir -p ~/private_keys
        echo \"\${{ secrets.API_KEY_CONTENT }}\" > ~/private_keys/AuthKey_\${{ secrets.API_KEY_ID }}.p8
        chmod 600 ~/private_keys/AuthKey_\${{ secrets.API_KEY_ID }}.p8
        echo \"API_KEY_ID=\${{ secrets.API_KEY_ID }}\" >> \$GITHUB_ENV
        echo \"ISSUER_ID=\${{ secrets.ISSUER_ID }}\" >> \$GITHUB_ENV

    - name: Upload to TestFlight (API Key)
      run: |
        echo \"开始上传到TestFlight (使用API Key)\"
        curl -s -X POST {$callbackUrl} \\
          -H \"Content-Type: application/json\" \\
          -d '{\"upload_id\": \"{$uploadId}\", \"status\": \"progress\", \"step\": \"step-testflight\", \"progress\": 80, \"message\": \"开始上传到TestFlight...\"}'

        if xcrun altool --upload-app \\
          -f \"\$IPA_FILE\" \\
          -t ios \\
          --apiKey \"\$API_KEY_ID\" \\
          --apiIssuer \"\$ISSUER_ID\" \\
          --verbose 2>&1 | tee upload_output.log; then
          echo \"✅ 上传成功！\"
          curl -s -X POST {$callbackUrl} \\
            -H \"Content-Type: application/json\" \\
            -d '{\"upload_id\": \"{$uploadId}\", \"status\": \"completed\", \"step\": \"step-testflight\", \"progress\": 100, \"message\": \"上传完成！\", \"result\": {\"bundle_id\": \"{$bundleId}\", \"app_name\": \"{$appName}\", \"version\": \"{$version}\", \"build\": \"{$build}\"}}'
        else
          echo \"❌ 上传失败\"
          ERROR_DETAILS=\$(cat upload_output.log)
          ERROR_MSG=\"上传到TestFlight失败\"
          if echo \"\$ERROR_DETAILS\" | grep -q \"bundle version must be higher\"; then
            ERROR_MSG=\"版本号重复：Bundle Version必须高于之前上传的版本\"
          elif echo \"\$ERROR_DETAILS\" | grep -q \"authentication failed\"; then
            ERROR_MSG=\"认证失败：请检查API Key是否正确\"
          fi
          curl -s -X POST {$callbackUrl} \\
            -H \"Content-Type: application/json\" \\
            -d \"{\\\"upload_id\\\": \\\"{$uploadId}\\\", \\\"status\\\": \\\"failed\\\", \\\"error\\\": \\\"\$ERROR_MSG\\\"}\"
          exit 1
        fi

    - name: Cleanup
      if: always()
      run: |
        rm -f app.ipa
        rm -rf ~/private_keys
        echo \"清理完成\"
";
  }

  /**
   * 生成高级Apple ID工作流（支持更多配置选项）
   */
  public function generateAdvancedAppleIDWorkflow(array $data): string
  {
    $uploadId = $data['upload_id'];
    $callbackUrl = $data['callback_url'];
    $downloadUrl = $data['download_url'] ?? '';
    $bundleId = $data['bundle_id'] ?? '';
    $appName = $data['app_name'] ?? '';
    $version = $data['version'] ?? '';
    $build = $data['build'] ?? '';
    $releaseNotes = $data['release_notes'] ?? '';

    return "name: Upload to TestFlight

# 高级工作流 - Apple ID认证
# 生成时间: {$this->getCurrentTime()}

on:
  workflow_dispatch:
    inputs:
      upload_id:
        description: 'Upload ID'
        required: true
        type: string

jobs:
  upload:
    runs-on: macos-latest
    timeout-minutes: 30

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Report Start
      run: |
        curl -s -X POST {$callbackUrl} \\
          -H \"Content-Type: application/json\" \\
          -d '{\"upload_id\": \"{$uploadId}\", \"status\": \"started\", \"step\": \"step-validate\", \"progress\": 10, \"message\": \"工作流已启动\"}'

    - name: Download IPA
      run: |
        echo \"下载IPA文件...\"
        curl -s -X POST {$callbackUrl} \\
          -H \"Content-Type: application/json\" \\
          -d '{\"upload_id\": \"{$uploadId}\", \"status\": \"progress\", \"step\": \"step-workflow\", \"progress\": 30, \"message\": \"下载IPA文件...\"}'

        DOWNLOAD_URL=\"{$downloadUrl}\"
        if curl -L -f -o app.ipa \"\$DOWNLOAD_URL\"; then
            echo \"IPA文件下载成功\"
            echo \"IPA_FILE=app.ipa\" >> \$GITHUB_ENV
        else
            echo \"IPA文件下载失败\"
            curl -s -X POST {$callbackUrl} \\
              -H \"Content-Type: application/json\" \\
              -d '{\"upload_id\": \"{$uploadId}\", \"status\": \"failed\", \"error\": \"IPA文件下载失败\"}'
            exit 1
        fi

    - name: Setup Apple ID
      run: |
        echo \"APPLE_ID=\${{ secrets.APPLE_ID }}\" >> \$GITHUB_ENV
        echo \"APP_PASSWORD=\${{ secrets.APP_PASSWORD }}\" >> \$GITHUB_ENV

    - name: Upload to TestFlight (Apple ID)
      run: |
        echo \"开始上传到TestFlight (使用Apple ID)\"
        curl -s -X POST {$callbackUrl} \\
          -H \"Content-Type: application/json\" \\
          -d '{\"upload_id\": \"{$uploadId}\", \"status\": \"progress\", \"step\": \"step-testflight\", \"progress\": 80, \"message\": \"开始上传到TestFlight...\"}'

        if xcrun altool --upload-app \\
          -f \"\$IPA_FILE\" \\
          -t ios \\
          -u \"\$APPLE_ID\" \\
          -p \"\$APP_PASSWORD\" \\
          --bundle-id \"{$bundleId}\" \\
          --bundle-short-version-string \"{$version}\" \\
          --bundle-version \"{$build}\" \\
          --verbose 2>&1 | tee upload_output.log; then
          echo \"✅ 上传成功！\"
          curl -s -X POST {$callbackUrl} \\
            -H \"Content-Type: application/json\" \\
            -d '{\"upload_id\": \"{$uploadId}\", \"status\": \"completed\", \"step\": \"step-testflight\", \"progress\": 100, \"message\": \"上传完成！\", \"result\": {\"bundle_id\": \"{$bundleId}\", \"app_name\": \"{$appName}\", \"version\": \"{$version}\", \"build\": \"{$build}\"}}'
        else
          echo \"❌ 上传失败\"
          ERROR_DETAILS=\$(cat upload_output.log)
          ERROR_MSG=\"上传到TestFlight失败\"
          if echo \"\$ERROR_DETAILS\" | grep -q \"bundle version must be higher\"; then
            ERROR_MSG=\"版本号重复：Bundle Version必须高于之前上传的版本\"
          elif echo \"\$ERROR_DETAILS\" | grep -q \"authentication failed\"; then
            ERROR_MSG=\"认证失败：请检查Apple ID和专属密码是否正确\"
          fi
          curl -s -X POST {$callbackUrl} \\
            -H \"Content-Type: application/json\" \\
            -d \"{\\\"upload_id\\\": \\\"{$uploadId}\\\", \\\"status\\\": \\\"failed\\\", \\\"error\\\": \\\"\$ERROR_MSG\\\"}\"
          exit 1
        fi

    - name: Cleanup
      if: always()
      run: |
        rm -f app.ipa
        echo \"清理完成\"
";
  }

  /**
   * 验证工作流语法
   */
  public function validateWorkflow(string $yamlContent): array
  {
    $errors = [];
    $lines = explode("\n", $yamlContent);

    for ($i = 0; $i < count($lines); $i++) {
      $line = $lines[$i];
      $lineNum = $i + 1;

      // 检查缩进是否为偶数空格
      $indent = strlen($line) - strlen(ltrim($line));
      if ($indent % 2 !== 0 && trim($line) !== '') {
        $errors[] = "Line {$lineNum}: 缩进应该是偶数个空格";
      }

      // 检查是否有制表符
      if (strpos($line, "\t") !== false) {
        $errors[] = "Line {$lineNum}: 不应该使用制表符，请使用空格";
      }

      // 检查冒号后是否有空格
      if (strpos($line, ':') !== false && !str_ends_with(trim($line), ':') && trim($line) !== '') {
        $colonIndex = strpos($line, ':');
        if ($colonIndex < strlen($line) - 1 && $line[$colonIndex + 1] !== ' ') {
          $errors[] = "Line {$lineNum}: 冒号后应该有空格";
        }
      }
    }

    return $errors;
  }
}
