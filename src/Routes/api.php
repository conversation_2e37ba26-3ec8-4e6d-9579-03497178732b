<?php

use App\Controllers\AuthController;
use App\Controllers\UploadController;
use App\Controllers\WorkflowController;
use App\Controllers\AdminController;
use Symfony\Component\Routing\Route;
use Symfony\Component\Routing\RouteCollection;

$routes = new RouteCollection();

// 认证相关路由
$routes->add('auth_login', new Route('/api/auth/login', [
    '_controller' => [AuthController::class, 'login']
], [], [], '', [], ['POST']));

$routes->add('auth_activate', new Route('/api/auth/activate', [
    '_controller' => [AuthController::class, 'activate']
], [], [], '', [], ['POST']));

$routes->add('auth_verify', new Route('/api/auth/verify', [
    '_controller' => [AuthController::class, 'verify']
], [], [], '', [], ['GET']));

$routes->add('auth_refresh', new Route('/api/auth/refresh', [
    '_controller' => [AuthController::class, 'refresh']
], [], [], '', [], ['POST']));

$routes->add('auth_validate_api_key', new Route('/api/auth/validate-api-key', [
    '_controller' => [AuthController::class, 'validateAPIKey']
], [], [], '', [], ['POST']));

$routes->add('auth_validate_apple_id', new Route('/api/auth/validate-apple-id', [
    '_controller' => [AuthController::class, 'validateAppleID']
], [], [], '', [], ['POST']));

// 上传相关路由
$routes->add('upload_ipa', new Route('/api/upload/ipa', [
    '_controller' => [UploadController::class, 'uploadIPA']
], [], [], '', [], ['POST']));

$routes->add('upload_records', new Route('/api/upload/records', [
    '_controller' => [UploadController::class, 'getRecords']
], [], [], '', [], ['GET']));

$routes->add('parse_ipa', new Route('/api/upload/parse-ipa', [
    '_controller' => [UploadController::class, 'parseIPA']
], [], [], '', [], ['POST']));

$routes->add('upload_file', new Route('/api/upload/file', [
    '_controller' => [UploadController::class, 'uploadFile']
], [], [], '', [], ['POST']));

$routes->add('upload_record_detail', new Route('/api/upload/records/{recordId}', [
    '_controller' => [UploadController::class, 'getRecordDetail']
], [], [], '', [], ['GET']));

$routes->add('upload_queue_status', new Route('/api/upload/queue', [
    '_controller' => [UploadController::class, 'getQueueStatus']
], [], [], '', [], ['GET']));

$routes->add('upload_webhook', new Route('/api/upload/webhook', [
    '_controller' => [UploadController::class, 'webhook']
], [], [], '', [], ['POST']));

$routes->add('upload_progress', new Route('/api/upload/progress/{uploadId}', [
    '_controller' => [UploadController::class, 'getProgress']
], [], [], '', [], ['GET']));

$routes->add('download_ipa', new Route('/api/upload/download/{recordId}', [
    '_controller' => [UploadController::class, 'downloadIPA']
], [], [], '', [], ['GET']));

$routes->add('generate_temp_download_link', new Route('/api/upload/temp-link/{recordId}', [
    '_controller' => [UploadController::class, 'generateTempDownloadLink']
], [], [], '', [], ['POST']));

$routes->add('temp_download_ipa', new Route('/api/upload/temp-download/{recordId}', [
    '_controller' => [UploadController::class, 'tempDownload']
], [], [], '', [], ['GET']));

$routes->add('workflow_status_callback', new Route('/api/upload/workflow-callback', [
    '_controller' => [UploadController::class, 'workflowCallback']
], [], [], '', [], ['POST']));

// 工作流相关路由
$routes->add('workflow_callback', new Route('/api/workflow/callback', [
    '_controller' => [WorkflowController::class, 'callback']
], [], [], '', [], ['POST']));

$routes->add('workflow_download_ipa', new Route('/download/ipa/{recordId}', [
    '_controller' => [WorkflowController::class, 'downloadIPA']
], [], [], '', [], ['GET']));

// 管理员路由
$routes->add('admin_create_user', new Route('/api/admin/users', [
    '_controller' => [AdminController::class, 'createUser']
], [], [], '', [], ['POST']));

$routes->add('admin_get_users', new Route('/api/admin/users', [
    '_controller' => [AdminController::class, 'getUsers']
], [], [], '', [], ['GET']));

$routes->add('admin_delete_user', new Route('/api/admin/users/{id}', [
    '_controller' => [AdminController::class, 'deleteUser']
], [], [], '', [], ['DELETE']));

$routes->add('admin_create_activation_code', new Route('/api/admin/activation-codes', [
    '_controller' => [AdminController::class, 'createActivationCode']
], [], [], '', [], ['POST']));

$routes->add('admin_get_activation_codes', new Route('/api/admin/activation-codes', [
    '_controller' => [AdminController::class, 'getActivationCodes']
], [], [], '', [], ['GET']));

$routes->add('admin_add_github_account', new Route('/api/admin/github-accounts', [
    '_controller' => [AdminController::class, 'addGitHubAccount']
], [], [], '', [], ['POST']));

$routes->add('admin_get_github_accounts', new Route('/api/admin/github-accounts', [
    '_controller' => [AdminController::class, 'getGitHubAccounts']
], [], [], '', [], ['GET']));

$routes->add('admin_update_github_account_status', new Route('/api/admin/github-accounts/{id}/status', [
    '_controller' => [AdminController::class, 'updateGitHubAccountStatus']
], [], [], '', [], ['PUT']));

$routes->add('admin_reset_github_account_usage', new Route('/api/admin/github-accounts/{id}/reset-usage', [
    '_controller' => [AdminController::class, 'resetGitHubAccountUsage']
], [], [], '', [], ['POST']));

$routes->add('admin_release_github_account', new Route('/api/admin/github-accounts/{id}/release', [
    '_controller' => [AdminController::class, 'releaseGitHubAccount']
], [], [], '', [], ['POST']));

$routes->add('admin_refresh_all_github_accounts', new Route('/api/admin/github-accounts/refresh-all', [
    '_controller' => [AdminController::class, 'refreshAllGitHubAccounts']
], [], [], '', [], ['POST']));

$routes->add('admin_reset_all_github_accounts_usage', new Route('/api/admin/github-accounts/reset-all-usage', [
    '_controller' => [AdminController::class, 'resetAllGitHubAccountsUsage']
], [], [], '', [], ['POST']));

$routes->add('admin_get_system_stats', new Route('/api/admin/stats', [
    '_controller' => [AdminController::class, 'getSystemStats']
], [], [], '', [], ['GET']));

return $routes;
