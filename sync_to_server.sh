#!/bin/bash

# 闲闲iOS工具 - 服务器同步脚本

# 远程服务器信息
REMOTE_USER="root"
REMOTE_HOST="************"
REMOTE_PATH="/data/www/api.ios.xxyx.cn"

# 本地工程目录（当前目录）
LOCAL_PATH="$(pwd)"

# 默认参数
INSTALL_DEPS=false
FORCE_INSTALL=false

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -i, --install-deps    安装/更新Composer依赖"
    echo "  -f, --force-install   强制重新安装所有依赖"
    echo "  -h, --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                   # 只同步代码，不安装依赖"
    echo "  $0 -i                # 同步代码并安装依赖"
    echo "  $0 -f                # 同步代码并强制重新安装依赖"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -i|--install-deps)
            INSTALL_DEPS=true
            shift
            ;;
        -f|--force-install)
            INSTALL_DEPS=true
            FORCE_INSTALL=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 需要排除的文件/目录
EXCLUDES=(
  ".git"
  "node_modules"
  "storage/uploads/ipa"
  "storage/logs"
  "storage/uploads/upload_tmp"
  "storage/uploads/icons"
  "*.log"
  "sync_to_server.sh"
)

# 构建rsync排除参数
EXCLUDE_ARGS=""
for item in "${EXCLUDES[@]}"; do
  EXCLUDE_ARGS+="--exclude=${item} "
done

echo "🚀 开始同步工程到服务器：${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}"
echo "本地目录：${LOCAL_PATH}"
echo "排除：${EXCLUDES[*]}"
echo ""

# 检查.env文件是否存在
if [ ! -f ".env" ]; then
    echo "⚠️  警告：未找到.env文件，请先配置环境变量"
    echo "请复制env.example为.env并配置必要的参数"
    exit 1
fi

echo "✅ 发现.env文件，将同步到服务器"
echo ""

# 执行rsync同步
echo "📤 正在同步文件..."
rsync -avz --delete $EXCLUDE_ARGS "$LOCAL_PATH/" "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}/"

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 文件同步完成！"
    echo ""
    echo "🔄 正在执行服务器端操作..."

    # 在服务器上执行必要的操作
    ssh "${REMOTE_USER}@${REMOTE_HOST}" << EOF
        cd ${REMOTE_PATH}

        # 依赖管理
        if [ "$INSTALL_DEPS" = true ]; then
            if [ "$FORCE_INSTALL" = true ]; then
                echo "� 强制重新安装Composer依赖..."
                rm -rf vendor/ composer.lock
                composer install --no-dev --optimize-autoloader
            else
                echo "�📦 检查并安装Composer依赖..."
                if [ ! -d "vendor" ] || [ ! -f "composer.lock" ]; then
                    echo "依赖目录不存在，执行安装..."
                    composer install --no-dev --optimize-autoloader
                else
                    echo "依赖已存在，执行更新..."
                    composer update --no-dev --optimize-autoloader
                fi
            fi
        else
            echo "📦 更新Composer autoload..."
            if [ -d "vendor" ]; then
                composer dump-autoload --optimize --no-dev
            else
                echo "⚠️  vendor目录不存在，建议运行: $0 -i"
                echo "📦 尝试安装基础依赖..."
                composer install --no-dev --optimize-autoloader
            fi
        fi

        echo "🔄 重启PHP-FPM..."
        systemctl restart php8.3-fpm

        echo "🔄 重启Caddy..."
        systemctl restart caddy

        echo "✅ 服务重启完成！"
EOF

    if [ $? -eq 0 ]; then
        echo ""
        echo "🎉 同步和部署完成！"
        echo ""
        echo "📋 已完成的操作："
        echo "- ✅ 文件同步"
        if [ "$INSTALL_DEPS" = true ]; then
            if [ "$FORCE_INSTALL" = true ]; then
                echo "- ✅ Composer依赖强制重新安装"
            else
                echo "- ✅ Composer依赖安装/更新"
            fi
        else
            echo "- ✅ Composer autoload更新"
        fi
        echo "- ✅ PHP-FPM重启"
        echo "- ✅ Caddy重启"
        echo ""
        echo "🔍 现在可以直接测试："
        echo "- php test_api.php"
        echo "- 访问前端页面: https://ios.xxyx.cn"
        echo ""
        echo "💡 使用提示："
        echo "- 日常开发: ./sync_to_server.sh (快速同步)"
        echo "- 同步+依赖: ./sync_to_server.sh -i"
        echo "- 强制重装: ./sync_to_server.sh -f"
    else
        echo ""
        echo "⚠️  文件同步成功，但服务重启失败"
        echo "请手动执行以下命令："
        echo "ssh ${REMOTE_USER}@${REMOTE_HOST}"
        echo "cd ${REMOTE_PATH}"
        echo "composer dump-autoload --optimize --no-dev"
        echo "sudo systemctl restart php8.3-fpm"
        echo "sudo systemctl restart caddy"
    fi
else
    echo ""
    echo "❌ 同步失败，请检查："
    echo "1. 网络连接是否正常"
    echo "2. SSH密钥是否配置"
    echo "3. 服务器目录是否存在"
    echo "4. 用户权限是否足够"
fi 