
2025-08-13 03:46:24.929 Verbose logging enabled.
2025-08-13 03:46:24.931 Will look for transporter at executable bundle relative path: /Applications/Xcode_15.4.app/Contents/SharedFrameworks/ContentDeliveryServices.framework/itms/bin/iTMSTransporter
2025-08-13 03:46:24.931 Looking for Transporter at path: /Applications/Xcode_15.4.app/Contents/SharedFrameworks/ContentDeliveryServices.framework/itms/bin/iTMSTransporter
2025-08-13 03:46:24.931 Looking for Transporter at path: /Applications/Xcode_15.4.app/Contents/Developer/usr/bin/../SharedFrameworks/ContentDeliveryServices.framework/itms/bin/iTMSTransporter
2025-08-13 03:46:24.931 Looking for Transporter at path: /Applications/Xcode_15.4.app/Contents/Developer/usr/bin/../itms/bin/iTMSTransporter
2025-08-13 03:46:24.931 Looking for Transporter at path: /Applications/Transporter.app/Contents/itms/bin/iTMSTransporter
2025-08-13 03:46:24.931 Looking for Transporter at path: /usr/local/itms/bin/iTMSTransporter
2025-08-13 03:46:24.931 Using default transporter location: /usr/local/itms/bin/iTMSTransporter
2025-08-13 03:46:24.932 iTMSTransporter is not available. Using REST APIs.
2025-08-13 03:46:25.684 
=======================
WEB SERVICE REQUEST 'generateAppleConnectToken'
           URL: https://contentdelivery.itunes.apple.com/WebObjects/MZLabelService.woa/json/MZITunesSoftwareService
       timeout: 1800
        method: POST
   httpHeaders: {
    "Content-Length" = 510;
    "Content-Type" = "application/json";
    "x-request-id" = "20250813034625-128";
    "x-tx-client-name" = altool;
    "x-tx-client-version" = "7.006 (15006)";
    "x-tx-method" = generateAppleConnectToken;
}
      httpBody: {
    id = "20250813034625-128";
    jsonrpc = "2.0";
    method = generateAppleConnectToken;
    params =     {
        Application = altool;
        ApplicationBundleId = "com.apple.itunes.altool";
        FrameworkVersions =         {
            "com.apple.itunes.connect.ITunesConnectFoundation" = "7.006 (15006)";
            "com.apple.itunes.connect.ITunesConnectFoundationExtras" = "7.006 (15006)";
            "com.apple.itunes.connect.ITunesPackage" = "7.006 (15006)";
        };
        OSIdentifier = "Mac OS X 14.7.6 (arm64)";
        Password = "**hidden value**";
        Username = "***";
        Version = "7.006 (15006)";
    };
}
=======================
2025-08-13 03:46:25.980 
=======================
WEB SERVICE RESPONSE (generateAppleConnectToken):
 status code: 200 (no error)
   MIME/TYPE: application/json
 httpHeaders: {
    "Accept-Language" = "en-US,en;q=0.9";
    "Cache-Control" = "private, no-cache, no-store, must-revalidate, max-age=0";
    Connection = "keep-alive";
    "Content-Encoding" = gzip;
    "Content-Length" = 1729;
    "Content-Type" = "application/json";
    Date = "Wed, 13 Aug 2025 03:46:26 GMT";
    Expires = "Thu, 24-Jul-2025 22:46:48 GMT";
    Pragma = "no-cache";
    Server = "daiquiri/5";
    "Strict-Transport-Security" = "max-age=31536000; includeSubDomains";
    "apple-originating-system" = MZContentDeliveryProducer;
    "apple-seq" = "0.0";
    "apple-timing-app" = "86 ms";
    "apple-tk" = false;
    b3 = "dc176a9cd9e19b4c89cb9011faecb35d-51200076535781be";
    "x-apple-application-instance" = 242114;
    "x-apple-application-site" = MR22;
    "x-apple-jingle-correlation-key" = 3QLWVHGZ4GNUZCOLSAI7V3FTLU;
    "x-apple-request-uuid" = "dc176a9c-d9e1-9b4c-89cb-9011faecb35d";
    "x-b3-spanid" = 51200076535781be;
    "x-b3-traceid" = dc176a9cd9e19b4c89cb9011faecb35d;
    "x-daiquiri-instance" = "daiquiri:13624001:mr85p00it-hyhk04174601:7987:25RELEASE91:daiquiri-amp-processing-shared-int-001-mr, daiquiri:18493001:mr85p00it-hyhk03154801:7987:25RELEASE91:daiquiri-amp-all-shared-ext-001-mr";
    "x-json-content-length" = 3690;
    "x-request-through-daiquiri-producer" = "mr-internal";
    "x-responding-instance" = "MZContentDeliveryProducer:242114:::";
    "x-webobjects-loadaverage" = 0;
}
    httpBody: {"id":"20250813034625-128","jsonrpc":"2.0","result":{"ApplicationSettings":{"SkipValidateFirenzeSPIUsage":false,"PerformServerVerification":true,"ITunesSoftwareServiceAllowedPlatforms":[{"restApiPlatformEnum":"IOS","validationMethod":"ios","platformCriteria":{"plistEntries":{"MinimumOSVersion":"","LSRequiresIPhoneOS":""}},"displayName":"iOS App","sdkID":"com.apple.platform.iphoneos","restApiPlatformUTI":"com.apple.ipa","type":1,"xmlID":"ios","serverID":"Purple","platformDirectory":"iPhoneOS.platform"},{"restApiPlatformEnum":"MAC_OS","validationMethod":"osx","platformCriteria":{"plistEntries":{"LSMinimumSystemVersion":""}},"displayName":"macOS App","sdkID":"com.apple.platform.macosx","xmlIDAliases":["macos"],"restApiPlatformUTI":"com.apple.pkg","type":2,"xmlID":"osx","serverID":"Firenze","platformDirectory":"MacOSX.platform"},{"validationMethod":"iap","type":3,"serverID":"InAppPurchase","displayName":"In-App Purchase"},{"restApiPlatformEnum":"TV_OS","validationMethod":"ios","platformCriteria":{"p
=======================
2025-08-13 03:46:25.986 Web service call (generateAppleConnectToken) result: {
    ApplicationSettings =     {
        AllowsMetadataOnlyInitialImport = 0;
        CDEnableImportErrorChecking = 1;
        ITunesCrashLogServiceAddress = "https://contentdelivery.itunes.apple.com/WebObjects/MZLabelService.woa/json/MZIT\U2026";
        ITunesSoftwareServiceAllowedPlatforms =         (
                        {
                displayName = "iOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        LSRequiresIPhoneOS = "";
                        MinimumOSVersion = "";
                    };
                };
                platformDirectory = "iPhoneOS.platform";
                restApiPlatformEnum = IOS;
                restApiPlatformUTI = "com.apple.ipa";
                sdkID = "com.apple.platform.iphoneos";
                serverID = Purple;
                type = 1;
                validationMethod = ios;
                xmlID = ios;
            },
                        {
                displayName = "macOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        LSMinimumSystemVersion = "";
                    };
                };
                platformDirectory = "MacOSX.platform";
                restApiPlatformEnum = "MAC_OS";
                restApiPlatformUTI = "com.apple.pkg";
                sdkID = "com.apple.platform.macosx";
                serverID = Firenze;
                type = 2;
                validationMethod = osx;
                xmlID = osx;
                xmlIDAliases =                 (
                    macos
                );
            },
                        {
                displayName = "In-App Purchase";
                serverID = InAppPurchase;
                type = 3;
                validationMethod = iap;
            },
                        {
                displayName = "tvOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        UIDeviceFamily = 3;
                    };
                };
                platformDirectory = "AppleTVOS.platform";
                restApiPlatformEnum = "TV_OS";
                restApiPlatformUTI = "com.apple.ipa";
                sdkID = "com.apple.platform.appletvos";
                serverID = AppleTVOS;
                type = 4;
                validationMethod = ios;
                xmlID = appletvos;
            },
                        {
                displayName = watchOS;
                platformDirectory = "WatchOS.platform";
                sdkID = "com.apple.platform.watchos";
                type = 6;
                validationMethod = ios;
            },
                        {
                displayName = "visionOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        UIDeviceFamily = 7;
                    };
                };
                platformDirectory = "XROS.platform";
                restApiPlatformEnum = "VISION_OS";
                restApiPlatformUTI = "com.apple.ipa";
                sdkID = "com.apple.platform.xros";
                serverID = XROS;
                type = 7;
                validationMethod = xros;
                xmlID = xros;
                xmlIDAliases =                 (
                    visionos
                );
            }
        );
        IsDisplayArtistVisible = 0;
        IsEnabledForMasteredForItunes = 0;
        IsISBNOptional = 1;
        IsPublicationDateOptional = 0;
        IsVendorIDEditablePreferenceEnabled = 1;
        MZWebServiceTimeOut = 900;
        MZWebServiceValidateSoftwareSPIUsageMaximumSize = 20971520;
        Notarization =         {
            Message = "Notarization of MacOS applications using altool has been decommissioned. Please \U2026";
            Status = UNAVAILABLE;
        };
        PerformServerVerification = 1;
        ShouldPricingTabBeDisplayed = 1;
        SkipValidateFirenzeSPIUsage = 0;
        SkipValidateProductErrors = 1;
        SkipValidatePurpleSPIUsage = 0;
        TerritoriesAllowedToAlterPlusQuality =         (
            JP
        );
        VerifyZipIntegrity = 1;
        softwareUserGuideFilePath = "https://help.apple.com/itc/apploader/";
        "\U2026 truncated \U2026" = "\U2026";
    };
    DSToken = "DAWTKNV323952cf8084a204fb20ab2508441a07d02d35e87fbb58f9a639e54c6d29af668b4538857\U2026";
    DSTokenCookieName = myacinfo;
    ShouldUseRESTAPIs = 0;
    StreamingSettings =     {
        AssetDescriptionStreamingEnabled = 0;
        LogStreamingEnabled = 1;
        MultipartUploadsEnabled = 1;
    };
}
2025-08-13 03:46:26.078 *** SESSION AUTH: Webservice call to create a transporter session.
2025-08-13 03:46:26.079 <?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>myacinfo</key>
	<string>DAWTKNV323952cf8084a204fb20ab2508441a07d02d35e87fbb58f9a639e54c6d29af668b45388578a400a8b9b4a43e3604984f7441366450b581d00c52abd1045b0c5c3107600ddc9fe8849b977ec2b7a6ab6c481cce16c4f8bc8fd0eef3df11f98b20108f6aab36ae6160ddc1db54cfdb696f8f5b68fadd30f9e5cec4d92e17de4461da0eb07bc58df2f3c222aa2bc122685fd25a741d4f37dc392c9b05412a9e029d1e275e9729b95839c5d9e0cd1d15472b67df0bc94ba7cad007bb0ac0b824a609d7061fd43603afb7520bd56bbc88b96f6a7db462aab86a197f76ac359c78c115521cea62ff9593602400984482a31a175e2d02ff807c42dda936508f849bc3e664be5818e7e032f7d9ad9aa0dcd33cc6efce3e8fdfc09a4e4e6deba2df01475fd712f0b6ca5b54dfdfeda42e42ab2bcc58d8efd47224b954646e6310bf7c9a1599c3bbbe3a71dff386be2631bd510ef32b9e0d6b031d9424bf700eb3bbc803cf78250098ef42847ac9fef13730366911ffdd4addb440fd79db6597900abff19f71e49585a47V3</string>
	<key>resultCode</key>
	<string>0</string>
</dict>
</plist>
2025-08-13 03:46:26.081 
=======================
WEB SERVICE REQUEST 'authenticateForSession'
           URL: https://contentdelivery.itunes.apple.com/WebObjects/MZLabelService.woa/json/MZContentDeliveryService
       timeout: 900
        method: POST
   httpHeaders: {
    "Content-Length" = 1558;
    "Content-Type" = "application/json";
    "x-request-id" = "20250813034626-693";
    "x-tx-client-name" = altool;
    "x-tx-client-version" = "7.006 (15006)";
    "x-tx-method" = authenticateForSession;
}
      httpBody: {
    id = "20250813034626-693";
    jsonrpc = "2.0";
    method = authenticateForSession;
    params =     {
        Application = altool;
        ApplicationBundleId = "com.apple.itunes.altool";
        "DS_PLIST" = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE plist PUBLIC \"-//Apple//DTD PLIST 1.0//EN\" \"http://www.apple.com/DTDs/PropertyList-1.0.dtd\">\n<plist version=\"1.0\">\n<dict>\n\t<key>myacinfo</key>\n\t<string>DAWTKNV323952cf8084a204fb20ab2508441a07d02d35e87fbb58f9a639e54c6d29af668b45388578a400a8b9b4a43e3604984f7441366450b581d00c52abd1045b0c5c3107600ddc9fe8849b977ec2b7a6ab6c481cce16c4f8bc8fd0eef3df11f98b20108f6aab36ae6160ddc1db54cfdb696f8f5b68fadd30f9e5cec4d92e17de4461da0eb07bc58df2f3c222aa2bc122685fd25a741d4f37dc392c9b05412a9e029d1e275e9729b95839c5d9e0cd1d15472b67df0bc94ba7cad007bb0ac0b824a609d7061fd43603afb7520bd56bbc88b96f6a7db462aab86a197f76ac359c78c115521cea62ff9593602400984482a31a175e2d02ff807c42dda936508f849bc3e664be5818e7e032f7d9ad9aa0dcd33cc6efce3e8fdfc09a4e4e6deba2df01475fd712f0b6ca5b54dfdfeda42e42ab2bcc58d8efd47224b954646e6310bf7c9a1599c3bbbe3a71dff386be2631bd510ef32b9e0d6b031d9424bf700eb3bbc803cf78250098ef42847ac9fef13730366911ffdd4addb440fd79db6597900abff19f71e49585a
        FrameworkVersions =         {
            "com.apple.itunes.connect.ITunesConnectFoundation" = "7.006 (15006)";
            "com.apple.itunes.connect.ITunesConnectFoundationExtras" = "7.006 (15006)";
            "com.apple.itunes.connect.ITunesPackage" = "7.006 (15006)";
        };
        OSIdentifier = "Mac OS X 14.7.6 (arm64)";
        Version = "7.006 (15006)";
    };
}
=======================
2025-08-13 03:46:26.446 
=======================
WEB SERVICE RESPONSE (authenticateForSession):
 status code: 200 (no error)
   MIME/TYPE: application/json
 httpHeaders: {
    "Accept-Language" = "en-US,en;q=0.9";
    "Cache-Control" = "private, no-cache, no-store, must-revalidate, max-age=0";
    Connection = "keep-alive";
    "Content-Encoding" = gzip;
    "Content-Length" = 1022;
    "Content-Type" = "application/json";
    Date = "Wed, 13 Aug 2025 03:46:26 GMT";
    Expires = "Thu, 24-Jul-2025 22:54:22 GMT";
    Pragma = "no-cache";
    Server = "daiquiri/5";
    "Strict-Transport-Security" = "max-age=31536000; includeSubDomains";
    "apple-originating-system" = MZContentDeliveryProducer;
    "apple-seq" = "0.0";
    "apple-timing-app" = "132 ms";
    "apple-tk" = false;
    b3 = "766d8bad4e326b8b8684c05bee9b0f0a-9de8f7901e162da7";
    "x-apple-application-instance" = 243915;
    "x-apple-application-site" = MR22;
    "x-apple-jingle-correlation-key" = OZWYXLKOGJVYXBUEYBN65GYPBI;
    "x-apple-request-uuid" = "766d8bad-4e32-6b8b-8684-c05bee9b0f0a";
    "x-b3-spanid" = 9de8f7901e162da7;
    "x-b3-traceid" = 766d8bad4e326b8b8684c05bee9b0f0a;
    "x-daiquiri-instance" = "daiquiri:13624001:mr85p00it-hyhk04174601:7987:25RELEASE91:daiquiri-amp-processing-shared-int-001-mr, daiquiri:18493001:mr85p00it-hyhk03154801:7987:25RELEASE91:daiquiri-amp-all-shared-ext-001-mr";
    "x-json-content-length" = 3952;
    "x-request-through-daiquiri-producer" = "mr-internal";
    "x-responding-instance" = "MZContentDeliveryProducer:243915:::";
    "x-webobjects-loadaverage" = 0;
}
    httpBody: {"id":"20250813034626-693","jsonrpc":"2.0","result":{"ApplicationSettings":{"MZWebServiceTimeOut":900,"ITunesSoftwareServiceAllowedPlatforms":[{"restApiPlatformEnum":"IOS","validationMethod":"ios","platformCriteria":{"plistEntries":{"MinimumOSVersion":"","LSRequiresIPhoneOS":""}},"displayName":"iOS App","sdkID":"com.apple.platform.iphoneos","restApiPlatformUTI":"com.apple.ipa","type":1,"xmlID":"ios","serverID":"Purple","platformDirectory":"iPhoneOS.platform"},{"restApiPlatformEnum":"MAC_OS","validationMethod":"osx","platformCriteria":{"plistEntries":{"LSMinimumSystemVersion":""}},"displayName":"macOS App","sdkID":"com.apple.platform.macosx","xmlIDAliases":["macos"],"restApiPlatformUTI":"com.apple.pkg","type":2,"xmlID":"osx","serverID":"Firenze","platformDirectory":"MacOSX.platform"},{"validationMethod":"iap","type":3,"serverID":"InAppPurchase","displayName":"In-App Purchase"},{"restApiPlatformEnum":"TV_OS","validationMethod":"ios","platformCriteria":{"plistEntries":{"UIDeviceFamily":3}},"display
=======================
2025-08-13 03:46:26.453 Web service call (authenticateForSession) result: {
    AllowedPlatforms =     (
                {
            displayName = "In-App Purchase";
            serverID = InAppPurchase;
            type = 3;
            validationMethod = iap;
        },
                {
            displayName = "macOS App";
            platformCriteria =             {
                plistEntries =                 {
                    LSMinimumSystemVersion = "";
                };
            };
            platformDirectory = "MacOSX.platform";
            restApiPlatformEnum = "MAC_OS";
            restApiPlatformUTI = "com.apple.pkg";
            sdkID = "com.apple.platform.macosx";
            serverID = Firenze;
            type = 2;
            validationMethod = osx;
            xmlID = osx;
            xmlIDAliases =             (
                macos
            );
        },
                {
            displayName = "iOS App";
            platformCriteria =             {
                plistEntries =                 {
                    LSRequiresIPhoneOS = "";
                    MinimumOSVersion = "";
                };
            };
            platformDirectory = "iPhoneOS.platform";
            restApiPlatformEnum = IOS;
            restApiPlatformUTI = "com.apple.ipa";
            sdkID = "com.apple.platform.iphoneos";
            serverID = Purple;
            type = 1;
            validationMethod = ios;
            xmlID = ios;
        },
                {
            displayName = watchOS;
            platformDirectory = "WatchOS.platform";
            sdkID = "com.apple.platform.watchos";
            type = 6;
            validationMethod = ios;
        },
                {
            displayName = "visionOS App";
            platformCriteria =             {
                plistEntries =                 {
                    UIDeviceFamily = 7;
                };
            };
            platformDirectory = "XROS.platform";
            restApiPlatformEnum = "VISION_OS";
            restApiPlatformUTI = "com.apple.ipa";
            sdkID = "com.apple.platform.xros";
            serverID = XROS;
            type = 7;
            validationMethod = xros;
            xmlID = xros;
            xmlIDAliases =             (
                visionos
            );
        },
                {
            displayName = "tvOS App";
            platformCriteria =             {
                plistEntries =                 {
                    UIDeviceFamily = 3;
                };
            };
            platformDirectory = "AppleTVOS.platform";
            restApiPlatformEnum = "TV_OS";
            restApiPlatformUTI = "com.apple.ipa";
            sdkID = "com.apple.platform.appletvos";
            serverID = AppleTVOS;
            type = 4;
            validationMethod = ios;
            xmlID = appletvos;
        }
    );
    ApplicationSettings =     {
        ITunesCrashLogServiceAddress = "https://contentdelivery.itunes.apple.com/WebObjects/MZLabelService.woa/json/MZIT\U2026";
        ITunesSoftwareServiceAllowedPlatforms =         (
                        {
                displayName = "iOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        LSRequiresIPhoneOS = "";
                        MinimumOSVersion = "";
                    };
                };
                platformDirectory = "iPhoneOS.platform";
                restApiPlatformEnum = IOS;
                restApiPlatformUTI = "com.apple.ipa";
                sdkID = "com.apple.platform.iphoneos";
                serverID = Purple;
                type = 1;
                validationMethod = ios;
                xmlID = ios;
            },
                        {
                displayName = "macOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        LSMinimumSystemVersion = "";
                    };
                };
                platformDirectory = "MacOSX.platform";
                restApiPlatformEnum = "MAC_OS";
                restApiPlatformUTI = "com.apple.pkg";
                sdkID = "com.apple.platform.macosx";
                serverID = Firenze;
                type = 2;
                validationMethod = osx;
                xmlID = osx;
                xmlIDAliases =                 (
                    macos
                );
            },
                        {
                displayName = "In-App Purchase";
                serverID = InAppPurchase;
                type = 3;
                validationMethod = iap;
            },
                        {
                displayName = "tvOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        UIDeviceFamily = 3;
                    };
                };
                platformDirectory = "AppleTVOS.platform";
                restApiPlatformEnum = "TV_OS";
                restApiPlatformUTI = "com.apple.ipa";
                sdkID = "com.apple.platform.appletvos";
                serverID = AppleTVOS;
                type = 4;
                validationMethod = ios;
                xmlID = appletvos;
            },
                        {
                displayName = watchOS;
                platformDirectory = "WatchOS.platform";
                sdkID = "com.apple.platform.watchos";
                type = 6;
                validationMethod = ios;
            },
                        {
                displayName = "visionOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        UIDeviceFamily = 7;
                    };
                };
                platformDirectory = "XROS.platform";
                restApiPlatformEnum = "VISION_OS";
                restApiPlatformUTI = "com.apple.ipa";
                sdkID = "com.apple.platform.xros";
                serverID = XROS;
                type = 7;
                validationMethod = xros;
                xmlID = xros;
                xmlIDAliases =                 (
                    visionos
                );
            }
        );
        MZWebServiceTimeOut = 900;
        Notarization =         {
            Message = "Notarization of MacOS applications using altool has been decommissioned. Please \U2026";
            Status = UNAVAILABLE;
        };
    };
    MultipartUploadsEnabled = 1;
    SessionExpiration = "2025-08-17T03:46:26.619Z";
    SessionId = "**hidden value**";
    SharedSecret = "**hidden value**";
    ShouldUseRESTAPIs = 0;
    StreamingSettings =     {
        AssetDescriptionStreamingEnabled = 0;
        LogStreamingEnabled = 1;
        MultipartUploadsEnabled = 1;
    };
    Success = 1;
    TxHeaders =     {
        jenga = 1;
    };
}
2025-08-13 03:46:26.508 *** SESSION AUTH: Jenga directives: {
    jenga = 1;
}
2025-08-13 03:46:26.512 <?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>myacinfo</key>
	<string>DAWTKNV323952cf8084a204fb20ab2508441a07d02d35e87fbb58f9a639e54c6d29af668b45388578a400a8b9b4a43e3604984f7441366450b581d00c52abd1045b0c5c3107600ddc9fe8849b977ec2b7a6ab6c481cce16c4f8bc8fd0eef3df11f98b20108f6aab36ae6160ddc1db54cfdb696f8f5b68fadd30f9e5cec4d92e17de4461da0eb07bc58df2f3c222aa2bc122685fd25a741d4f37dc392c9b05412a9e029d1e275e9729b95839c5d9e0cd1d15472b67df0bc94ba7cad007bb0ac0b824a609d7061fd43603afb7520bd56bbc88b96f6a7db462aab86a197f76ac359c78c115521cea62ff9593602400984482a31a175e2d02ff807c42dda936508f849bc3e664be5818e7e032f7d9ad9aa0dcd33cc6efce3e8fdfc09a4e4e6deba2df01475fd712f0b6ca5b54dfdfeda42e42ab2bcc58d8efd47224b954646e6310bf7c9a1599c3bbbe3a71dff386be2631bd510ef32b9e0d6b031d9424bf700eb3bbc803cf78250098ef42847ac9fef13730366911ffdd4addb440fd79db6597900abff19f71e49585a47V3</string>
	<key>resultCode</key>
	<string>0</string>
</dict>
</plist>
2025-08-13 03:46:26.513 
=======================
WEB SERVICE REQUEST 'allowedPlatformsWithAuthentication'
           URL: https://contentdelivery.itunes.apple.com/WebObjects/MZLabelService.woa/json/MZITunesProducerService
       timeout: 900
        method: POST
   httpHeaders: {
    "Content-Length" = 1570;
    "Content-Type" = "application/json";
    "x-request-id" = "20250813034626-232";
    "x-session-digest" = 82efc1c6ad59db2ce2ec1d06f9a66909;
    "x-session-id" = "CIyuDBIQfemv3uadSYi9Va+xzwrH4g==";
    "x-tx-client-name" = altool;
    "x-tx-client-version" = "7.006 (15006)";
    "x-tx-method" = allowedPlatformsWithAuthentication;
}
      httpBody: {
    id = "20250813034626-232";
    jsonrpc = "2.0";
    method = allowedPlatformsWithAuthentication;
    params =     {
        Application = altool;
        ApplicationBundleId = "com.apple.itunes.altool";
        "DS_PLIST" = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE plist PUBLIC \"-//Apple//DTD PLIST 1.0//EN\" \"http://www.apple.com/DTDs/PropertyList-1.0.dtd\">\n<plist version=\"1.0\">\n<dict>\n\t<key>myacinfo</key>\n\t<string>DAWTKNV323952cf8084a204fb20ab2508441a07d02d35e87fbb58f9a639e54c6d29af668b45388578a400a8b9b4a43e3604984f7441366450b581d00c52abd1045b0c5c3107600ddc9fe8849b977ec2b7a6ab6c481cce16c4f8bc8fd0eef3df11f98b20108f6aab36ae6160ddc1db54cfdb696f8f5b68fadd30f9e5cec4d92e17de4461da0eb07bc58df2f3c222aa2bc122685fd25a741d4f37dc392c9b05412a9e029d1e275e9729b95839c5d9e0cd1d15472b67df0bc94ba7cad007bb0ac0b824a609d7061fd43603afb7520bd56bbc88b96f6a7db462aab86a197f76ac359c78c115521cea62ff9593602400984482a31a175e2d02ff807c42dda936508f849bc3e664be5818e7e032f7d9ad9aa0dcd33cc6efce3e8fdfc09a4e4e6deba2df01475fd712f0b6ca5b54dfdfeda42e42ab2bcc58d8efd47224b954646e6310bf7c9a1599c3bbbe3a71dff386be2631bd510ef32b9e0d6b031d9424bf700eb3bbc803cf78250098ef42847ac9fef13730366911ffdd4addb440fd79db6597900abff19f71e49585a
        FrameworkVersions =         {
            "com.apple.itunes.connect.ITunesConnectFoundation" = "7.006 (15006)";
            "com.apple.itunes.connect.ITunesConnectFoundationExtras" = "7.006 (15006)";
            "com.apple.itunes.connect.ITunesPackage" = "7.006 (15006)";
        };
        OSIdentifier = "Mac OS X 14.7.6 (arm64)";
        Version = "7.006 (15006)";
    };
}
=======================
2025-08-13 03:46:26.775 
=======================
WEB SERVICE RESPONSE (allowedPlatformsWithAuthentication):
 status code: 200 (no error)
   MIME/TYPE: application/json
 httpHeaders: {
    "Accept-Language" = "en-US,en;q=0.9";
    "Cache-Control" = "private, no-cache, no-store, must-revalidate, max-age=0";
    Connection = "keep-alive";
    "Content-Encoding" = gzip;
    "Content-Length" = 1241;
    "Content-Type" = "application/json";
    Date = "Wed, 13 Aug 2025 03:46:26 GMT";
    Expires = "Thu, 24-Jul-2025 22:48:06 GMT";
    Pragma = "no-cache";
    Server = "daiquiri/5";
    "Strict-Transport-Security" = "max-age=31536000; includeSubDomains";
    "apple-originating-system" = MZContentDeliveryProducer;
    "apple-seq" = "0.0";
    "apple-timing-app" = "44 ms";
    "apple-tk" = false;
    b3 = "61301a0d2020386062986ec88a118229-f18ab2cd68e95fc2";
    "x-apple-application-instance" = 242515;
    "x-apple-application-site" = MR22;
    "x-apple-jingle-correlation-key" = MEYBUDJAEA4GAYUYN3EIUEMCFE;
    "x-apple-request-uuid" = "61301a0d-**************-6ec88a118229";
    "x-b3-spanid" = f18ab2cd68e95fc2;
    "x-b3-traceid" = 61301a0d2020386062986ec88a118229;
    "x-daiquiri-instance" = "daiquiri:13624001:mr85p00it-hyhk04174601:7987:25RELEASE91:daiquiri-amp-processing-shared-int-001-mr, daiquiri:18493001:mr85p00it-hyhk03154801:7987:25RELEASE91:daiquiri-amp-all-shared-ext-001-mr";
    "x-json-content-length" = 4456;
    "x-request-through-daiquiri-producer" = "mr-internal";
    "x-responding-instance" = "MZContentDeliveryProducer:242515:::";
    "x-webobjects-loadaverage" = 0;
}
    httpBody: {"id":"20250813034626-232","jsonrpc":"2.0","result":{"ApplicationSettings":{"SkipValidateFirenzeSPIUsage":false,"PerformServerVerification":true,"ITunesSoftwareServiceAllowedPlatforms":[{"restApiPlatformEnum":"IOS","validationMethod":"ios","platformCriteria":{"plistEntries":{"MinimumOSVersion":"","LSRequiresIPhoneOS":""}},"displayName":"iOS App","sdkID":"com.apple.platform.iphoneos","restApiPlatformUTI":"com.apple.ipa","type":1,"xmlID":"ios","serverID":"Purple","platformDirectory":"iPhoneOS.platform"},{"restApiPlatformEnum":"MAC_OS","validationMethod":"osx","platformCriteria":{"plistEntries":{"LSMinimumSystemVersion":""}},"displayName":"macOS App","sdkID":"com.apple.platform.macosx","xmlIDAliases":["macos"],"restApiPlatformUTI":"com.apple.pkg","type":2,"xmlID":"osx","serverID":"Firenze","platformDirectory":"MacOSX.platform"},{"validationMethod":"iap","type":3,"serverID":"InAppPurchase","displayName":"In-App Purchase"},{"restApiPlatformEnum":"TV_OS","validationMethod":"ios","platformCriteria":{"p
=======================
2025-08-13 03:46:26.783 Web service call (allowedPlatformsWithAuthentication) result: {
    AllowedPlatforms =     (
                {
            displayName = "In-App Purchase";
            serverID = InAppPurchase;
            type = 3;
            validationMethod = iap;
        },
                {
            displayName = "macOS App";
            platformCriteria =             {
                plistEntries =                 {
                    LSMinimumSystemVersion = "";
                };
            };
            platformDirectory = "MacOSX.platform";
            restApiPlatformEnum = "MAC_OS";
            restApiPlatformUTI = "com.apple.pkg";
            sdkID = "com.apple.platform.macosx";
            serverID = Firenze;
            type = 2;
            validationMethod = osx;
            xmlID = osx;
            xmlIDAliases =             (
                macos
            );
        },
                {
            displayName = "iOS App";
            platformCriteria =             {
                plistEntries =                 {
                    LSRequiresIPhoneOS = "";
                    MinimumOSVersion = "";
                };
            };
            platformDirectory = "iPhoneOS.platform";
            restApiPlatformEnum = IOS;
            restApiPlatformUTI = "com.apple.ipa";
            sdkID = "com.apple.platform.iphoneos";
            serverID = Purple;
            type = 1;
            validationMethod = ios;
            xmlID = ios;
        },
                {
            displayName = watchOS;
            platformDirectory = "WatchOS.platform";
            sdkID = "com.apple.platform.watchos";
            type = 6;
            validationMethod = ios;
        },
                {
            displayName = "visionOS App";
            platformCriteria =             {
                plistEntries =                 {
                    UIDeviceFamily = 7;
                };
            };
            platformDirectory = "XROS.platform";
            restApiPlatformEnum = "VISION_OS";
            restApiPlatformUTI = "com.apple.ipa";
            sdkID = "com.apple.platform.xros";
            serverID = XROS;
            type = 7;
            validationMethod = xros;
            xmlID = xros;
            xmlIDAliases =             (
                visionos
            );
        },
                {
            displayName = "tvOS App";
            platformCriteria =             {
                plistEntries =                 {
                    UIDeviceFamily = 3;
                };
            };
            platformDirectory = "AppleTVOS.platform";
            restApiPlatformEnum = "TV_OS";
            restApiPlatformUTI = "com.apple.ipa";
            sdkID = "com.apple.platform.appletvos";
            serverID = AppleTVOS;
            type = 4;
            validationMethod = ios;
            xmlID = appletvos;
        }
    );
    ApplicationSettings =     {
        AllowsMetadataOnlyInitialImport = 0;
        CDEnableImportErrorChecking = 1;
        ITunesCrashLogServiceAddress = "https://contentdelivery.itunes.apple.com/WebObjects/MZLabelService.woa/json/MZIT\U2026";
        ITunesSoftwareServiceAllowedPlatforms =         (
                        {
                displayName = "iOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        LSRequiresIPhoneOS = "";
                        MinimumOSVersion = "";
                    };
                };
                platformDirectory = "iPhoneOS.platform";
                restApiPlatformEnum = IOS;
                restApiPlatformUTI = "com.apple.ipa";
                sdkID = "com.apple.platform.iphoneos";
                serverID = Purple;
                type = 1;
                validationMethod = ios;
                xmlID = ios;
            },
                        {
                displayName = "macOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        LSMinimumSystemVersion = "";
                    };
                };
                platformDirectory = "MacOSX.platform";
                restApiPlatformEnum = "MAC_OS";
                restApiPlatformUTI = "com.apple.pkg";
                sdkID = "com.apple.platform.macosx";
                serverID = Firenze;
                type = 2;
                validationMethod = osx;
                xmlID = osx;
                xmlIDAliases =                 (
                    macos
                );
            },
                        {
                displayName = "In-App Purchase";
                serverID = InAppPurchase;
                type = 3;
                validationMethod = iap;
            },
                        {
                displayName = "tvOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        UIDeviceFamily = 3;
                    };
                };
                platformDirectory = "AppleTVOS.platform";
                restApiPlatformEnum = "TV_OS";
                restApiPlatformUTI = "com.apple.ipa";
                sdkID = "com.apple.platform.appletvos";
                serverID = AppleTVOS;
                type = 4;
                validationMethod = ios;
                xmlID = appletvos;
            },
                        {
                displayName = watchOS;
                platformDirectory = "WatchOS.platform";
                sdkID = "com.apple.platform.watchos";
                type = 6;
                validationMethod = ios;
            },
                        {
                displayName = "visionOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        UIDeviceFamily = 7;
                    };
                };
                platformDirectory = "XROS.platform";
                restApiPlatformEnum = "VISION_OS";
                restApiPlatformUTI = "com.apple.ipa";
                sdkID = "com.apple.platform.xros";
                serverID = XROS;
                type = 7;
                validationMethod = xros;
                xmlID = xros;
                xmlIDAliases =                 (
                    visionos
                );
            }
        );
        IsDisplayArtistVisible = 0;
        IsEnabledForMasteredForItunes = 0;
        IsISBNOptional = 1;
        IsPublicationDateOptional = 0;
        IsVendorIDEditablePreferenceEnabled = 1;
        MZWebServiceTimeOut = 900;
        MZWebServiceValidateSoftwareSPIUsageMaximumSize = 20971520;
        Notarization =         {
            Message = "Notarization of MacOS applications using altool has been decommissioned. Please \U2026";
            Status = UNAVAILABLE;
        };
        PerformServerVerification = 1;
        ShouldPricingTabBeDisplayed = 1;
        SkipValidateFirenzeSPIUsage = 0;
        SkipValidateProductErrors = 1;
        SkipValidatePurpleSPIUsage = 0;
        TerritoriesAllowedToAlterPlusQuality =         (
            JP
        );
        VerifyZipIntegrity = 1;
        softwareUserGuideFilePath = "https://help.apple.com/itc/apploader/";
        "\U2026 truncated \U2026" = "\U2026";
    };
    SessionExpiration = "2025-08-17T03:46:26.932Z";
    ShouldUseRESTAPIs = 0;
    StreamingSettings =     {
        AssetDescriptionStreamingEnabled = 0;
        LogStreamingEnabled = 1;
        MultipartUploadsEnabled = 1;
    };
    Success = 1;
}
2025-08-13 03:46:26.940 Executing: /usr/bin/ditto -xk app.ipa /var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/app.ipa
2025-08-13 03:46:27.044 Finished: /usr/bin/ditto -xk app.ipa /var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/app.ipa with status 0
2025-08-13 03:46:27.046 *** SESSION AUTH: AuthenticationContext already has a session id and shared secret; reusing.
2025-08-13 03:46:27.046 <?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>myacinfo</key>
	<string>DAWTKNV323952cf8084a204fb20ab2508441a07d02d35e87fbb58f9a639e54c6d29af668b45388578a400a8b9b4a43e3604984f7441366450b581d00c52abd1045b0c5c3107600ddc9fe8849b977ec2b7a6ab6c481cce16c4f8bc8fd0eef3df11f98b20108f6aab36ae6160ddc1db54cfdb696f8f5b68fadd30f9e5cec4d92e17de4461da0eb07bc58df2f3c222aa2bc122685fd25a741d4f37dc392c9b05412a9e029d1e275e9729b95839c5d9e0cd1d15472b67df0bc94ba7cad007bb0ac0b824a609d7061fd43603afb7520bd56bbc88b96f6a7db462aab86a197f76ac359c78c115521cea62ff9593602400984482a31a175e2d02ff807c42dda936508f849bc3e664be5818e7e032f7d9ad9aa0dcd33cc6efce3e8fdfc09a4e4e6deba2df01475fd712f0b6ca5b54dfdfeda42e42ab2bcc58d8efd47224b954646e6310bf7c9a1599c3bbbe3a71dff386be2631bd510ef32b9e0d6b031d9424bf700eb3bbc803cf78250098ef42847ac9fef13730366911ffdd4addb440fd79db6597900abff19f71e49585a47V3</string>
	<key>resultCode</key>
	<string>0</string>
</dict>
</plist>
2025-08-13 03:46:27.046 
=======================
WEB SERVICE REQUEST 'lookupSoftwareForBundleId'
           URL: https://contentdelivery.itunes.apple.com/WebObjects/MZLabelService.woa/json/MZITunesSoftwareService
       timeout: 900
        method: POST
   httpHeaders: {
    "Content-Length" = 1620;
    "Content-Type" = "application/json";
    "x-request-id" = "20250813034627-649";
    "x-session-digest" = 10d1a2be3dc783c1ebabd570cebe0de7;
    "x-session-id" = "CIyuDBIQfemv3uadSYi9Va+xzwrH4g==";
    "x-tx-client-name" = altool;
    "x-tx-client-version" = "7.006 (15006)";
    "x-tx-method" = lookupSoftwareForBundleId;
}
      httpBody: {
    id = "20250813034627-649";
    jsonrpc = "2.0";
    method = lookupSoftwareForBundleId;
    params =     {
        Application = altool;
        ApplicationBundleId = "com.apple.itunes.altool";
        BundleId = "com.xxapp.getidfa";
        "DS_PLIST" = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE plist PUBLIC \"-//Apple//DTD PLIST 1.0//EN\" \"http://www.apple.com/DTDs/PropertyList-1.0.dtd\">\n<plist version=\"1.0\">\n<dict>\n\t<key>myacinfo</key>\n\t<string>DAWTKNV323952cf8084a204fb20ab2508441a07d02d35e87fbb58f9a639e54c6d29af668b45388578a400a8b9b4a43e3604984f7441366450b581d00c52abd1045b0c5c3107600ddc9fe8849b977ec2b7a6ab6c481cce16c4f8bc8fd0eef3df11f98b20108f6aab36ae6160ddc1db54cfdb696f8f5b68fadd30f9e5cec4d92e17de4461da0eb07bc58df2f3c222aa2bc122685fd25a741d4f37dc392c9b05412a9e029d1e275e9729b95839c5d9e0cd1d15472b67df0bc94ba7cad007bb0ac0b824a609d7061fd43603afb7520bd56bbc88b96f6a7db462aab86a197f76ac359c78c115521cea62ff9593602400984482a31a175e2d02ff807c42dda936508f849bc3e664be5818e7e032f7d9ad9aa0dcd33cc6efce3e8fdfc09a4e4e6deba2df01475fd712f0b6ca5b54dfdfeda42e42ab2bcc58d8efd47224b954646e6310bf7c9a1599c3bbbe3a71dff386be2631bd510ef32b9e0d6b031d9424bf700eb3bbc803cf78250098ef42847ac9fef13730366911ffdd4addb440fd79db6597900abff19f71e49585a
        FrameworkVersions =         {
            "com.apple.itunes.connect.ITunesConnectFoundation" = "7.006 (15006)";
            "com.apple.itunes.connect.ITunesConnectFoundationExtras" = "7.006 (15006)";
            "com.apple.itunes.connect.ITunesPackage" = "7.006 (15006)";
        };
        OSIdentifier = "Mac OS X 14.7.6 (arm64)";
        SoftwareTypeEnum = Purple;
        Version = "7.006 (15006)";
    };
}
=======================
2025-08-13 03:46:27.379 
=======================
WEB SERVICE RESPONSE (lookupSoftwareForBundleId):
 status code: 200 (no error)
   MIME/TYPE: application/json
 httpHeaders: {
    "Accept-Language" = "en-US,en;q=0.9";
    "Cache-Control" = "private, no-cache, no-store, must-revalidate, max-age=0";
    Connection = "keep-alive";
    "Content-Encoding" = gzip;
    "Content-Length" = 1794;
    "Content-Type" = "application/json";
    Date = "Wed, 13 Aug 2025 03:46:27 GMT";
    Expires = "Thu, 24-Jul-2025 22:46:53 GMT";
    Pragma = "no-cache";
    Server = "daiquiri/5";
    "Strict-Transport-Security" = "max-age=31536000; includeSubDomains";
    "apple-originating-system" = MZContentDeliveryProducer;
    "apple-seq" = "0.0";
    "apple-timing-app" = "118 ms";
    "apple-tk" = false;
    b3 = "5dca4345e7f9f44d2a7fb77d72d4dad2-ba903f042317451a";
    "x-apple-application-instance" = 242116;
    "x-apple-application-site" = MR22;
    "x-apple-jingle-correlation-key" = LXFEGRPH7H2E2KT7W56XFVG22I;
    "x-apple-request-uuid" = "5dca4345-e7f9-f44d-2a7f-b77d72d4dad2";
    "x-b3-spanid" = ba903f042317451a;
    "x-b3-traceid" = 5dca4345e7f9f44d2a7fb77d72d4dad2;
    "x-daiquiri-instance" = "daiquiri:13624001:mr85p00it-hyhk04174601:7987:25RELEASE91:daiquiri-amp-processing-shared-int-001-mr, daiquiri:18493001:mr85p00it-hyhk03154801:7987:25RELEASE91:daiquiri-amp-all-shared-ext-001-mr";
    "x-json-content-length" = 4185;
    "x-request-through-daiquiri-producer" = "mr-internal";
    "x-responding-instance" = "MZContentDeliveryProducer:242116:::";
    "x-webobjects-loadaverage" = 0;
}
    httpBody: {"id":"20250813034627-649","jsonrpc":"2.0","result":{"ProviderPublicId":"7e498df2-7f54-412b-8dc8-b79d10cf26e5","DisplayKeys":["Application","Version Number","SKU Number","Primary Language","Copyright","Type","Apple ID"],"Applications":{"获取IDFA 1.0 (iOS App)":"**********"},"ValidIconSizes":["57x57"],"SessionExpiration":"2025-08-17T03:46:27.460Z","Attributes":[{"Apple ID":"**********","AppleID":"**********","WWDRIdentifier":"WQ375JW66J","ExistingBundleIdentifier":"com.xxapp.getidfa","SKU Number":"20250619","Version Number":"3","Copyright":"2025 GetIdfa Inc.","IconURL":"https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/49/4a/47/494a47b5-e3f5-97cc-6f62-7a73b74754e7/AppIcon-0-0-1x_U007epad-0-1-85-220.png/0x0ss-100.png","Type":"iOS App","Primary Language":"zh-Hans","ShortVersion":"1.0","ReservedBundleIdentifier":"com.xxapp.getidfa","DidOptInToMacAppStore":"true","DidOptInToXROSAppStore":"true","SoftwareTypeEnum":"Purple","Application":"获取IDFA","MarketingVersion":"1.0"}],"Success":true,"StreamingSe
=======================
2025-08-13 03:46:27.385 Web service call (lookupSoftwareForBundleId) result: {
    ApplicationSettings =     {
        AllowsMetadataOnlyInitialImport = 0;
        CDEnableImportErrorChecking = 1;
        ITunesCrashLogServiceAddress = "https://contentdelivery.itunes.apple.com/WebObjects/MZLabelService.woa/json/MZIT\U2026";
        ITunesSoftwareServiceAllowedPlatforms =         (
                        {
                displayName = "iOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        LSRequiresIPhoneOS = "";
                        MinimumOSVersion = "";
                    };
                };
                platformDirectory = "iPhoneOS.platform";
                restApiPlatformEnum = IOS;
                restApiPlatformUTI = "com.apple.ipa";
                sdkID = "com.apple.platform.iphoneos";
                serverID = Purple;
                type = 1;
                validationMethod = ios;
                xmlID = ios;
            },
                        {
                displayName = "macOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        LSMinimumSystemVersion = "";
                    };
                };
                platformDirectory = "MacOSX.platform";
                restApiPlatformEnum = "MAC_OS";
                restApiPlatformUTI = "com.apple.pkg";
                sdkID = "com.apple.platform.macosx";
                serverID = Firenze;
                type = 2;
                validationMethod = osx;
                xmlID = osx;
                xmlIDAliases =                 (
                    macos
                );
            },
                        {
                displayName = "In-App Purchase";
                serverID = InAppPurchase;
                type = 3;
                validationMethod = iap;
            },
                        {
                displayName = "tvOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        UIDeviceFamily = 3;
                    };
                };
                platformDirectory = "AppleTVOS.platform";
                restApiPlatformEnum = "TV_OS";
                restApiPlatformUTI = "com.apple.ipa";
                sdkID = "com.apple.platform.appletvos";
                serverID = AppleTVOS;
                type = 4;
                validationMethod = ios;
                xmlID = appletvos;
            },
                        {
                displayName = watchOS;
                platformDirectory = "WatchOS.platform";
                sdkID = "com.apple.platform.watchos";
                type = 6;
                validationMethod = ios;
            },
                        {
                displayName = "visionOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        UIDeviceFamily = 7;
                    };
                };
                platformDirectory = "XROS.platform";
                restApiPlatformEnum = "VISION_OS";
                restApiPlatformUTI = "com.apple.ipa";
                sdkID = "com.apple.platform.xros";
                serverID = XROS;
                type = 7;
                validationMethod = xros;
                xmlID = xros;
                xmlIDAliases =                 (
                    visionos
                );
            }
        );
        IsDisplayArtistVisible = 0;
        IsEnabledForMasteredForItunes = 0;
        IsISBNOptional = 1;
        IsPublicationDateOptional = 0;
        IsVendorIDEditablePreferenceEnabled = 1;
        MZWebServiceTimeOut = 900;
        MZWebServiceValidateSoftwareSPIUsageMaximumSize = 20971520;
        Notarization =         {
            Message = "Notarization of MacOS applications using altool has been decommissioned. Please \U2026";
            Status = UNAVAILABLE;
        };
        PerformServerVerification = 1;
        ShouldPricingTabBeDisplayed = 1;
        SkipValidateFirenzeSPIUsage = 0;
        SkipValidateProductErrors = 1;
        SkipValidatePurpleSPIUsage = 0;
        TerritoriesAllowedToAlterPlusQuality =         (
            JP
        );
        VerifyZipIntegrity = 1;
        softwareUserGuideFilePath = "https://help.apple.com/itc/apploader/";
        "\U2026 truncated \U2026" = "\U2026";
    };
    Applications =     {
        "\U83b7\U53d6IDFA 1.0 (iOS App)" = **********;
    };
    Attributes =     (
                {
            "Apple ID" = **********;
            AppleID = **********;
            Application = "\U83b7\U53d6IDFA";
            Copyright = "2025 GetIdfa Inc.";
            DidOptInToMacAppStore = true;
            DidOptInToXROSAppStore = true;
            ExistingBundleIdentifier = "com.xxapp.getidfa";
            IconURL = "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/49/4a/47/494a47b5-e3f5-97c\U2026";
            MarketingVersion = "1.0";
            "Primary Language" = "zh-Hans";
            ReservedBundleIdentifier = "com.xxapp.getidfa";
            "SKU Number" = 20250619;
            ShortVersion = "1.0";
            SoftwareTypeEnum = Purple;
            Type = "iOS App";
            "Version Number" = 3;
            WWDRIdentifier = WQ375JW66J;
        }
    );
    DisallowedPaths =     (
        "SC_Info"
    );
    DisplayKeys =     (
        Application,
        "Version Number",
        "SKU Number",
        "Primary Language",
        Copyright,
        Type,
        "Apple ID"
    );
    PerformCodesignVerification = "anchor apple generic and certificate 1[field.1.2.840.113635.*********] exists an\U2026";
    PerformIconSizeVerification = true;
    PerformIconVerification = true;
    PerformZipFilenameVerification = true;
    ProviderName = WQ375JW66J;
    ProviderPublicId = "7e498df2-7f54-412b-8dc8-b79d10cf26e5";
    SessionExpiration = "2025-08-17T03:46:27.460Z";
    ShouldUseRESTAPIs = 0;
    SkipValidateSupportedArchitectures = true;
    StreamingSettings =     {
        AssetDescriptionStreamingEnabled = 0;
        LogStreamingEnabled = 1;
        MultipartUploadsEnabled = 1;
    };
    Success = 1;
    ValidIconSizes =     (
        57x57
    );
}
2025-08-13 03:46:27.452 ‘IosApplicationArchiveExpander’, cleanupDestinationDirectory: ‘/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/app.ipa’
2025-08-13 03:46:27.457 <?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>myacinfo</key>
	<string>DAWTKNV323952cf8084a204fb20ab2508441a07d02d35e87fbb58f9a639e54c6d29af668b45388578a400a8b9b4a43e3604984f7441366450b581d00c52abd1045b0c5c3107600ddc9fe8849b977ec2b7a6ab6c481cce16c4f8bc8fd0eef3df11f98b20108f6aab36ae6160ddc1db54cfdb696f8f5b68fadd30f9e5cec4d92e17de4461da0eb07bc58df2f3c222aa2bc122685fd25a741d4f37dc392c9b05412a9e029d1e275e9729b95839c5d9e0cd1d15472b67df0bc94ba7cad007bb0ac0b824a609d7061fd43603afb7520bd56bbc88b96f6a7db462aab86a197f76ac359c78c115521cea62ff9593602400984482a31a175e2d02ff807c42dda936508f849bc3e664be5818e7e032f7d9ad9aa0dcd33cc6efce3e8fdfc09a4e4e6deba2df01475fd712f0b6ca5b54dfdfeda42e42ab2bcc58d8efd47224b954646e6310bf7c9a1599c3bbbe3a71dff386be2631bd510ef32b9e0d6b031d9424bf700eb3bbc803cf78250098ef42847ac9fef13730366911ffdd4addb440fd79db6597900abff19f71e49585a47V3</string>
	<key>resultCode</key>
	<string>0</string>
</dict>
</plist>
2025-08-13 03:46:27.458 
=======================
WEB SERVICE REQUEST 'shouldUseFeatures'
           URL: https://contentdelivery.itunes.apple.com/WebObjects/MZLabelService.woa/json/MZITunesProducerService
       timeout: 900
        method: POST
   httpHeaders: {
    "Content-Length" = 1634;
    "Content-Type" = "application/json";
    "x-request-id" = "20250813034627-744";
    "x-session-digest" = 432cb2ad52db8d8a1f801bf9ea08d4c7;
    "x-session-id" = "CIyuDBIQfemv3uadSYi9Va+xzwrH4g==";
    "x-tx-client-name" = altool;
    "x-tx-client-version" = "7.006 (15006)";
    "x-tx-method" = shouldUseFeatures;
}
      httpBody: {
    id = "20250813034627-744";
    jsonrpc = "2.0";
    method = shouldUseFeatures;
    params =     {
        AppleId = **********;
        Application = altool;
        ApplicationBundleId = "com.apple.itunes.altool";
        "DS_PLIST" = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE plist PUBLIC \"-//Apple//DTD PLIST 1.0//EN\" \"http://www.apple.com/DTDs/PropertyList-1.0.dtd\">\n<plist version=\"1.0\">\n<dict>\n\t<key>myacinfo</key>\n\t<string>DAWTKNV323952cf8084a204fb20ab2508441a07d02d35e87fbb58f9a639e54c6d29af668b45388578a400a8b9b4a43e3604984f7441366450b581d00c52abd1045b0c5c3107600ddc9fe8849b977ec2b7a6ab6c481cce16c4f8bc8fd0eef3df11f98b20108f6aab36ae6160ddc1db54cfdb696f8f5b68fadd30f9e5cec4d92e17de4461da0eb07bc58df2f3c222aa2bc122685fd25a741d4f37dc392c9b05412a9e029d1e275e9729b95839c5d9e0cd1d15472b67df0bc94ba7cad007bb0ac0b824a609d7061fd43603afb7520bd56bbc88b96f6a7db462aab86a197f76ac359c78c115521cea62ff9593602400984482a31a175e2d02ff807c42dda936508f849bc3e664be5818e7e032f7d9ad9aa0dcd33cc6efce3e8fdfc09a4e4e6deba2df01475fd712f0b6ca5b54dfdfeda42e42ab2bcc58d8efd47224b954646e6310bf7c9a1599c3bbbe3a71dff386be2631bd510ef32b9e0d6b031d9424bf700eb3bbc803cf78250098ef42847ac9fef13730366911ffdd4addb440fd79db6597900abff19f71e49585a
        FrameworkVersions =         {
            "com.apple.itunes.connect.ITunesConnectFoundation" = "7.006 (15006)";
            "com.apple.itunes.connect.ITunesConnectFoundationExtras" = "7.006 (15006)";
            "com.apple.itunes.connect.ITunesPackage" = "7.006 (15006)";
        };
        OSIdentifier = "Mac OS X 14.7.6 (arm64)";
        ProviderPublicId = "7e498df2-7f54-412b-8dc8-b79d10cf26e5";
        Version = "7.006 (15006)";
    };
}
=======================
2025-08-13 03:46:27.780 
=======================
WEB SERVICE RESPONSE (shouldUseFeatures):
 status code: 200 (no error)
   MIME/TYPE: application/json
 httpHeaders: {
    "Accept-Language" = "en-US,en;q=0.9";
    "Cache-Control" = "private, no-cache, no-store, must-revalidate, max-age=0";
    Connection = "keep-alive";
    "Content-Encoding" = gzip;
    "Content-Length" = 1218;
    "Content-Type" = "application/json";
    Date = "Wed, 13 Aug 2025 03:46:27 GMT";
    Expires = "Fri, 08-Aug-2025 23:08:32 GMT";
    Pragma = "no-cache";
    Server = "daiquiri/5";
    "Strict-Transport-Security" = "max-age=31536000; includeSubDomains";
    "apple-originating-system" = MZContentDeliveryProducer;
    "apple-seq" = "0.0";
    "apple-timing-app" = "92 ms";
    "apple-tk" = false;
    b3 = "e577599f8c9a1cfc2d25306127b22455-1eee52506d971888";
    "x-apple-application-instance" = 240117;
    "x-apple-application-site" = MR22;
    "x-apple-jingle-correlation-key" = 4V3VTH4MTIOPYLJFGBQSPMREKU;
    "x-apple-request-uuid" = "e577599f-8c9a-1cfc-2d25-306127b22455";
    "x-b3-spanid" = 1eee52506d971888;
    "x-b3-traceid" = e577599f8c9a1cfc2d25306127b22455;
    "x-daiquiri-instance" = "daiquiri:13624001:mr85p00it-hyhk04174601:7987:25RELEASE91:daiquiri-amp-processing-shared-int-001-mr, daiquiri:18493001:mr85p00it-hyhk03154801:7987:25RELEASE91:daiquiri-amp-all-shared-ext-001-mr";
    "x-json-content-length" = 2999;
    "x-request-through-daiquiri-producer" = "mr-internal";
    "x-responding-instance" = "MZContentDeliveryProducer:240117:::";
    "x-webobjects-loadaverage" = 0;
}
    httpBody: {"id":"20250813034627-744","jsonrpc":"2.0","result":{"ApplicationSettings":{"SkipValidateFirenzeSPIUsage":false,"PerformServerVerification":true,"ITunesSoftwareServiceAllowedPlatforms":[{"restApiPlatformEnum":"IOS","validationMethod":"ios","platformCriteria":{"plistEntries":{"MinimumOSVersion":"","LSRequiresIPhoneOS":""}},"displayName":"iOS App","sdkID":"com.apple.platform.iphoneos","restApiPlatformUTI":"com.apple.ipa","type":1,"xmlID":"ios","serverID":"Purple","platformDirectory":"iPhoneOS.platform"},{"restApiPlatformEnum":"MAC_OS","validationMethod":"osx","platformCriteria":{"plistEntries":{"LSMinimumSystemVersion":""}},"displayName":"macOS App","sdkID":"com.apple.platform.macosx","xmlIDAliases":["macos"],"restApiPlatformUTI":"com.apple.pkg","type":2,"xmlID":"osx","serverID":"Firenze","platformDirectory":"MacOSX.platform"},{"validationMethod":"iap","type":3,"serverID":"InAppPurchase","displayName":"In-App Purchase"},{"restApiPlatformEnum":"TV_OS","validationMethod":"ios","platformCriteria":{"p
=======================
2025-08-13 03:46:27.785 Web service call (shouldUseFeatures) result: {
    ApplicationSettings =     {
        AllowsMetadataOnlyInitialImport = 0;
        CDEnableImportErrorChecking = 1;
        ITunesCrashLogServiceAddress = "https://contentdelivery.itunes.apple.com/WebObjects/MZLabelService.woa/json/MZIT\U2026";
        ITunesSoftwareServiceAllowedPlatforms =         (
                        {
                displayName = "iOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        LSRequiresIPhoneOS = "";
                        MinimumOSVersion = "";
                    };
                };
                platformDirectory = "iPhoneOS.platform";
                restApiPlatformEnum = IOS;
                restApiPlatformUTI = "com.apple.ipa";
                sdkID = "com.apple.platform.iphoneos";
                serverID = Purple;
                type = 1;
                validationMethod = ios;
                xmlID = ios;
            },
                        {
                displayName = "macOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        LSMinimumSystemVersion = "";
                    };
                };
                platformDirectory = "MacOSX.platform";
                restApiPlatformEnum = "MAC_OS";
                restApiPlatformUTI = "com.apple.pkg";
                sdkID = "com.apple.platform.macosx";
                serverID = Firenze;
                type = 2;
                validationMethod = osx;
                xmlID = osx;
                xmlIDAliases =                 (
                    macos
                );
            },
                        {
                displayName = "In-App Purchase";
                serverID = InAppPurchase;
                type = 3;
                validationMethod = iap;
            },
                        {
                displayName = "tvOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        UIDeviceFamily = 3;
                    };
                };
                platformDirectory = "AppleTVOS.platform";
                restApiPlatformEnum = "TV_OS";
                restApiPlatformUTI = "com.apple.ipa";
                sdkID = "com.apple.platform.appletvos";
                serverID = AppleTVOS;
                type = 4;
                validationMethod = ios;
                xmlID = appletvos;
            },
                        {
                displayName = watchOS;
                platformDirectory = "WatchOS.platform";
                sdkID = "com.apple.platform.watchos";
                type = 6;
                validationMethod = ios;
            },
                        {
                displayName = "visionOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        UIDeviceFamily = 7;
                    };
                };
                platformDirectory = "XROS.platform";
                restApiPlatformEnum = "VISION_OS";
                restApiPlatformUTI = "com.apple.ipa";
                sdkID = "com.apple.platform.xros";
                serverID = XROS;
                type = 7;
                validationMethod = xros;
                xmlID = xros;
                xmlIDAliases =                 (
                    visionos
                );
            }
        );
        IsDisplayArtistVisible = 0;
        IsEnabledForMasteredForItunes = 0;
        IsISBNOptional = 1;
        IsPublicationDateOptional = 0;
        IsVendorIDEditablePreferenceEnabled = 1;
        MZWebServiceTimeOut = 900;
        MZWebServiceValidateSoftwareSPIUsageMaximumSize = 20971520;
        Notarization =         {
            Message = "Notarization of MacOS applications using altool has been decommissioned. Please \U2026";
            Status = UNAVAILABLE;
        };
        PerformServerVerification = 1;
        ShouldPricingTabBeDisplayed = 1;
        SkipValidateFirenzeSPIUsage = 0;
        SkipValidateProductErrors = 1;
        SkipValidatePurpleSPIUsage = 0;
        TerritoriesAllowedToAlterPlusQuality =         (
            JP
        );
        VerifyZipIntegrity = 1;
        softwareUserGuideFilePath = "https://help.apple.com/itc/apploader/";
        "\U2026 truncated \U2026" = "\U2026";
    };
    SessionExpiration = "2025-08-17T03:46:27.916Z";
    ShouldUseFeatures =     {
        shouldUseContentDelivery = 1;
        shouldUseRESTAPIs = 1;
    };
    ShouldUseRESTAPIs = 0;
    StreamingSettings =     {
        AssetDescriptionStreamingEnabled = 0;
        LogStreamingEnabled = 1;
        MultipartUploadsEnabled = 1;
    };
    Success = 1;
}
2025-08-13 03:46:27.954 
====================
= Using feature shouldUseContentDelivery based on server side response.
====================
2025-08-13 03:46:27.954 
====================
= Using feature shouldUseRESTAPIs based on NSUserDefaults settings.
====================
2025-08-13 03:46:27.955 
###################################
# Using ContentDelivery Framework #
###################################
2025-08-13 03:46:28.941 DEBUG: [ContentDelivery.Uploader] ContentDelivery version 2.154.1 (15401).
2025-08-13 03:46:28.943 DEBUG: [ContentDelivery.Uploader] Created log file at path '/Users/<USER>/Library/Logs/ContentDelivery/com.apple.itunes.altool/com.apple.itunes.altool_Upload_2025-08-13_03-46-28_938.txt'.
2025-08-13 03:46:28.945 DEBUG: [ContentDelivery.Uploader] Show Progress: Contacting Apple Services…
2025-08-13 03:46:28.947 DEBUG: [ContentDelivery.Uploader] Created NSURLSession with ID=com.apple.cds_0BF0F4C4-3BAC-4088-864B-BDA6C02DB718, timeoutIntervalForRequest=900s, timeoutIntervalForResource=604800s
2025-08-13 03:46:28.948 DEBUG: [ContentDelivery.Uploader] CDUploader has network connectivity (May use cellular if available).
2025-08-13 03:46:28.949 DEBUG: [ContentDelivery.Uploader] CDUploader now has network connectivity.
2025-08-13 03:46:28.949 DEBUG: [ContentDelivery.Uploader] 
=======================================
REQUEST CREATE CONTAINER (ASSET_UPLOAD) REQUEST:
         URL: https://contentdelivery.itunes.apple.com/MZContentDeliveryService/iris/provider/7e498df2-7f54-412b-8dc8-b79d10cf26e5/v1/builds
     timeout: 900 seconds
      method: POST
 httpHeaders: {
    Accept = "application/json";
    "Accept-Language" = "en-US";
    "Content-Type" = "application/json";
    Cookie = "myacinfo=DAWTKNV323952cf8084a204fb20ab2508441a07d02d35e87fbb58f9a639e54c6d29af668b45388578a400a8b9b4a43e3604984f7441366450b581d00c52abd1045b0c5c3107600ddc9fe8849b977ec2b7a6ab6c481cce16c4f8bc8fd0eef3df11f98b20108f6aab36ae6160ddc1db54cfdb696f8f5b68fadd30f9e5cec4d92e17de4461da0eb07bc58df2f3c222aa2bc122685fd25a741d4f37dc392c9b05412a9e029d1e275e9729b95839c5d9e0cd1d15472b67df0bc94ba7cad007bb0ac0b824a609d7061fd43603afb7520bd56bbc88b96f6a7db462aab86a197f76ac359c78c115521cea62ff9593602400984482a31a175e2d02ff807c42dda936508f849bc3e664be5818e7e032f7d9ad9aa0dcd33cc6efce3e8fdfc09a4e4e6deba2df01475fd712f0b6ca5b54dfdfeda42e42ab2bcc58d8efd47224b954646e6310bf7c9a1599c3bbbe3a71dff386be2631bd510ef32b9e0d6b031d9424bf700eb3bbc803cf78250098ef42847ac9fef13730366911ffdd4addb440fd79db6597900abff19f71e49585a47V3";
    "User-Agent" = "altool/7.006-15006 (Macintosh; macOS 14.7.6 23H626 (arm64)) ContentDelivery/2.154.1-15401";
}
    httpBody: {"data":{"attributes":{"cfBundleShortVersionString":"2.0","cfBundleVersion":"1","platform":"IOS"},"relationships":{"app":{"data":{"id":"**********","type":"apps"}}},"type":"builds"}}
========================================
2025-08-13 03:46:29.111 DEBUG: [ContentDelivery.Uploader] Download task 1 sent 182 bytes (182 of 182 bytes sent).
2025-08-13 03:46:29.578 DEBUG: [ContentDelivery.Uploader] Download task 1 did write 479 bytes.
2025-08-13 03:46:29.579 DEBUG: [ContentDelivery.Uploader] Download task 1 did write file: file:///var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/CFNetworkDownload_CAkQzt.tmp
2025-08-13 03:46:29.580 DEBUG: [ContentDelivery.Uploader] 
=======================================
REQUEST CREATE CONTAINER (ASSET_UPLOAD) RESPONSE:
         URL: https://contentdelivery.itunes.apple.com/MZContentDeliveryService/iris/provider/7e498df2-7f54-412b-8dc8-b79d10cf26e5/v1/builds
 status code: 409 (conflict)
 httpHeaders: {
    "Content-Length" = 479;
    "Content-Type" = "application/json";
    Date = "Wed, 13 Aug 2025 03:46:29 GMT";
    Server = "daiquiri/5";
    "Set-Cookie" = "dqsid=; Expires=Thu, 01 Jan 1970 00:00:00 GMT; Path=/; Secure; HTTPOnly, dqsid=; Expires=Thu, 01 Jan 1970 00:00:00 GMT; Path=/; Secure; HTTPOnly, dqsid=***; Max-Age=1800; Expires=Wed, 13 Aug 2025 04:16:29 GMT; Path=/; Secure; HTTPOnly";
    "Strict-Transport-Security" = "max-age=31536000; includeSubDomains";
    "apple-originating-system" = MZContentDeliveryService;
    "apple-seq" = "0.0";
    "apple-timing-app" = 84;
    "apple-tk" = false;
    b3 = "933502eda6e5f94c132b1afabaeb5d3c-c3a1191ade29f9d0";
    "x-apple-jingle-correlation-key" = SM2QF3NG4X4UYEZLDL5LV225HQ;
    "x-apple-request-uuid" = "933502ed-a6e5-f94c-132b-1afabaeb5d3c";
    "x-b3-spanid" = c3a1191ade29f9d0;
    "x-b3-traceid" = 933502eda6e5f94c132b1afabaeb5d3c;
    "x-daiquiri-instance" = "daiquiri:33624001:pv50p00it-hyhk10063801:7987:25RELEASE91:daiquiri-amp-processing-shared-int-001-pv, daiquiri:18493001:mr85p00it-hyhk03154801:7987:25RELEASE91:daiquiri-amp-all-shared-ext-001-mr";
    "x-daiquiri-rate-limit-timing-user" = 0;
    "x-daiquiri-rate-limit-user" = "user-hour-lim:3600;user-hour-rem:3599;";
}
    httpBody: {
  "errors" : [ {
    "id" : "a0497bae-277e-43ee-8844-235666fee062",
    "status" : "409",
    "code" : "ENTITY_ERROR.ATTRIBUTE.INVALID.DUPLICATE",
    "title" : "The provided entity includes an attribute with a value that has already been used",
    "detail" : "The bundle version must be higher than the previously uploaded version.",
    "source" : {
      "pointer" : "/data/attributes/cfBundleVersion"
    },
    "meta" : {
      "previousBundleVersion" : "1"
    }
  } ]
}
=======================================
2025-08-13 03:46:29.582 Failed to request an upload. (
    "Error Domain=ContentDelivery Code=-19232 \"The provided entity includes an attribute with a value that has already been used\" UserInfo={NSLocalizedFailureReason=The bundle version must be higher than the previously uploaded version: \U20181\U2019. (ID: a0497bae-277e-43ee-8844-235666fee062), NSUnderlyingError=0x600002ff12f0 {Error Domain=IrisAPI Code=-19241 \"The provided entity includes an attribute with a value that has already been used\" UserInfo={status=409, detail=The bundle version must be higher than the previously uploaded version., source={\n    pointer = \"/data/attributes/cfBundleVersion\";\n}, id=a0497bae-277e-43ee-8844-235666fee062, code=ENTITY_ERROR.ATTRIBUTE.INVALID.DUPLICATE, title=The provided entity includes an attribute with a value that has already been used, meta={\n    previousBundleVersion = 1;\n}, NSLocalizedDescription=The provided entity includes an attribute with a value that has already been used, NSLocalizedFailureReason=The bundle version must be higher than the previously 
)
2025-08-13 03:46:29.583 Has version info conflict: YES, Previous version info: 1
2025-08-13 03:46:29.585 *** Error: Error uploading 'app.ipa'.
2025-08-13 03:46:29.586 *** Error: The provided entity includes an attribute with a value that has already been used The bundle version must be higher than the previously uploaded version: ‘1’. (ID: a0497bae-277e-43ee-8844-235666fee062) (-19232)
 {
    NSLocalizedDescription = "The provided entity includes an attribute with a value that has already been used";
    NSLocalizedFailureReason = "The bundle version must be higher than the previously uploaded version: \U20181\U2019. (ID: a0497bae-277e-43ee-8844-235666fee062)";
    NSUnderlyingError = "Error Domain=IrisAPI Code=-19241 \"The provided entity includes an attribute with a value that has already been used\" UserInfo={status=409, detail=The bundle version must be higher than the previously uploaded version., source={\n    pointer = \"/data/attributes/cfBundleVersion\";\n}, id=a0497bae-277e-43ee-8844-235666fee062, code=ENTITY_ERROR.ATTRIBUTE.INVALID.DUPLICATE, title=The provided entity includes an attribute with a value that has already been used, meta={\n    previousBundleVersion = 1;\n}, NSLocalizedDescription=The provided entity includes an attribute with a value that has already been used, NSLocalizedFailureReason=The bundle version must be higher than the previously uploaded version.}";
    "iris-code" = "ENTITY_ERROR.ATTRIBUTE.INVALID.DUPLICATE";
    previousBundleVersion = 1;
}
✅ 上传成功！
{"success":true,"message":"\u72b6\u6001\u66f4\u65b0\u6210\u529f"}