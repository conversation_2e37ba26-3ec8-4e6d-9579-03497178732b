#!/bin/bash

# 前端代码同步脚本

# 远程服务器信息
REMOTE_USER="root"
REMOTE_HOST="************"
REMOTE_PATH="/data/www/ios.xxyx.cn"
LOCAL_FRONTEND_PATH="./frontend"

# 备份配置
BACKUP_DIR="/data/backups/frontend"
BACKUP_ENABLED=true

echo "🌐 前端代码同步工具"
echo "======================="

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -f, --force     强制同步，不询问确认"
    echo "  -n, --dry-run   预览模式，只显示将要同步的文件"
    echo "  -b, --backup    创建备份（默认启用）"
    echo "  --no-backup     不创建备份"
    echo "  -h, --help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0              # 交互式同步"
    echo "  $0 -f           # 强制同步"
    echo "  $0 -n           # 预览模式"
    echo "  $0 --no-backup  # 不创建备份"
}

# 解析命令行参数
FORCE_SYNC=false
DRY_RUN=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--force)
            FORCE_SYNC=true
            shift
            ;;
        -n|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -b|--backup)
            BACKUP_ENABLED=true
            shift
            ;;
        --no-backup)
            BACKUP_ENABLED=false
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查本地前端目录
if [ ! -d "$LOCAL_FRONTEND_PATH" ]; then
    echo "❌ 本地前端目录不存在: $LOCAL_FRONTEND_PATH"
    exit 1
fi

echo "📋 同步配置:"
echo "- 本地路径: $LOCAL_FRONTEND_PATH"
echo "- 远程服务器: $REMOTE_USER@$REMOTE_HOST"
echo "- 远程路径: $REMOTE_PATH"
echo "- 备份: $([ "$BACKUP_ENABLED" = true ] && echo "启用" || echo "禁用")"
echo "- 模式: $([ "$DRY_RUN" = true ] && echo "预览模式" || echo "实际同步")"
echo ""

# 显示本地文件统计
echo "📁 本地文件统计:"
total_files=$(find "$LOCAL_FRONTEND_PATH" -type f | wc -l)
total_size=$(du -sh "$LOCAL_FRONTEND_PATH" | cut -f1)
echo "- 文件数量: $total_files"
echo "- 总大小: $total_size"
echo ""

# 显示主要文件
echo "📄 主要文件:"
find "$LOCAL_FRONTEND_PATH" -name "*.html" -o -name "*.js" -o -name "*.css" | head -10
echo ""

# 预览模式
if [ "$DRY_RUN" = true ]; then
    echo "🔍 预览模式 - 将要同步的更改:"
    rsync -avz --delete --dry-run \
      --exclude=".DS_Store" \
      --exclude="*.tmp" \
      --exclude="node_modules/" \
      --exclude=".git/" \
      "$LOCAL_FRONTEND_PATH/" "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}/"
    echo ""
    echo "ℹ️  这是预览模式，没有实际同步文件"
    echo "要执行实际同步，请运行: $0 -f"
    exit 0
fi

# 确认同步
if [ "$FORCE_SYNC" = false ]; then
    read -p "确认同步前端代码到服务器？(y/N): " confirm
    if [[ $confirm != [yY] ]]; then
        echo "❌ 同步已取消"
        exit 0
    fi
fi

# 创建备份
if [ "$BACKUP_ENABLED" = true ]; then
    echo "💾 创建远程备份..."
    backup_name="frontend_backup_$(date +%Y%m%d_%H%M%S)"
    
    ssh "${REMOTE_USER}@${REMOTE_HOST}" << EOF
        # 创建备份目录
        mkdir -p ${BACKUP_DIR}
        
        # 如果远程目录存在，创建备份
        if [ -d "${REMOTE_PATH}" ]; then
            echo "创建备份: ${backup_name}"
            tar -czf "${BACKUP_DIR}/${backup_name}.tar.gz" -C "$(dirname ${REMOTE_PATH})" "$(basename ${REMOTE_PATH})" 2>/dev/null || true
            
            # 保留最近5个备份
            cd ${BACKUP_DIR}
            ls -t *.tar.gz 2>/dev/null | tail -n +6 | xargs rm -f 2>/dev/null || true
            
            echo "✅ 备份创建完成"
        else
            echo "⚠️  远程目录不存在，跳过备份"
        fi
EOF
    
    if [ $? -eq 0 ]; then
        echo "✅ 备份创建成功"
    else
        echo "⚠️  备份创建失败，但继续同步"
    fi
fi

# 开始同步
echo "🚀 开始同步前端代码..."

# 确保远程目录存在
ssh "${REMOTE_USER}@${REMOTE_HOST}" "mkdir -p ${REMOTE_PATH}"

# 使用rsync同步
rsync -avz --delete \
  --exclude=".DS_Store" \
  --exclude="*.tmp" \
  --exclude="node_modules/" \
  --exclude=".git/" \
  --exclude="*.log" \
  --progress \
  "$LOCAL_FRONTEND_PATH/" "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}/"

# 检查同步结果
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 文件同步成功！"
    
    # 设置权限和优化
    echo "🔧 优化部署环境..."
    ssh "${REMOTE_USER}@${REMOTE_HOST}" << EOF
        cd ${REMOTE_PATH}
        
        # 设置正确的权限
        find . -type d -exec chmod 755 {} \;
        find . -type f -exec chmod 644 {} \;
        
        # 设置所有者
        chown -R www-data:www-data . 2>/dev/null || chown -R caddy:caddy . 2>/dev/null || true
        
        # 创建必要的目录
        mkdir -p logs temp
        
        # 生成文件清单
        find . -type f > .file_manifest
        
        echo "✅ 环境优化完成"
        echo "📊 部署统计:"
        echo "- 文件数量: \$(find . -type f | wc -l)"
        echo "- 目录数量: \$(find . -type d | wc -l)"
        echo "- 总大小: \$(du -sh . | cut -f1)"
EOF
    
    echo ""
    echo "🎉 前端部署完成！"
    echo ""
    echo "📋 访问地址:"
    echo "- 🏠 主页: https://ios.xxyx.cn"
    echo "- 🔐 登录: https://ios.xxyx.cn/login.html"
    echo "- 📊 管理: https://ios.xxyx.cn/admin.html"
    echo "- 🧪 测试: https://ios.xxyx.cn/test_auth.html"
    echo ""
    echo "🔍 验证步骤:"
    echo "1. 访问主页检查页面加载"
    echo "2. 测试登录功能 (admin/admin123456)"
    echo "3. 验证页面跳转和权限控制"
    echo "4. 检查管理员功能"
    
    if [ "$BACKUP_ENABLED" = true ]; then
        echo ""
        echo "💾 备份信息:"
        echo "- 备份位置: ${REMOTE_HOST}:${BACKUP_DIR}/"
        echo "- 备份文件: ${backup_name}.tar.gz"
    fi
    
else
    echo ""
    echo "❌ 前端同步失败"
    echo ""
    echo "🔧 故障排除:"
    echo "1. 检查网络连接: ping $REMOTE_HOST"
    echo "2. 检查SSH连接: ssh $REMOTE_USER@$REMOTE_HOST 'echo 连接成功'"
    echo "3. 检查远程路径权限: ssh $REMOTE_USER@$REMOTE_HOST 'ls -la $(dirname $REMOTE_PATH)'"
    echo "4. 重试同步: $0 -f"
    
    exit 1
fi
