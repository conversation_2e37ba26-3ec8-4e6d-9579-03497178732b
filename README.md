# XIOS - iOS应用管理工具

一个基于PHP 8.3和MongoDB 7的iOS应用上传平台，支持通过GitHub Actions自动上传IPA文件到App Store Connect。

## 🚀 功能特性

### 用户系统
- 管理员创建普通用户账号
- 激活码激活机制（时效性）
- JWT身份验证
- 用户上传历史记录

### GitHub账号池管理
- 管理员管理GitHub账号池
- 自动选择最优账号（剩余时间最多）
- 监控GitHub Actions剩余时间
- 并发控制（单账号单任务）

### IPA上传功能
- 支持拖拽上传IPA文件
- 自动解析IPA内容（Bundle ID、版本、图标等）
- 支持API Key和Apple ID两种认证方式
- 实时上传进度显示
- 上传队列管理

### 实时通信
- HTTP轮询实时更新
- 上传进度实时显示
- 队列状态实时监控

## 🛠️ 技术栈

- **后端**: PHP 8.3
- **数据库**: MongoDB 7
- **HTTP客户端**: Guzzle
- **JWT**: Firebase JWT
- **前端**: HTML5 + JavaScript
- **部署**: PHP-FPM

## 📁 项目结构

```
XIOS/
├── src/                    # PHP源代码
│   ├── Config/            # 配置类
│   ├── Controllers/       # 控制器
│   ├── Database/          # 数据库连接
│   ├── Models/            # 数据模型
│   ├── Routes/            # 路由定义
│   └── Services/          # 业务服务
├── frontend/              # 前端文件
│   ├── login.html         # 统一登录入口
│   ├── index.html         # 主界面（根据角色显示）
│   ├── admin.html         # 管理后台
│   └── js/               # JavaScript文件
├── public/               # 公共访问目录
├── storage/              # 文件存储
├── vendor/               # Composer依赖
├── composer.json         # 项目依赖
├── env.example           # 环境变量模板
├── sync_to_server.sh     # 后端同步脚本
├── sync_frontend.sh      # 前端同步脚本
└── deploy.sh            # 部署脚本
```

## 🚀 快速开始

### 1. 环境要求

- PHP 8.3+
- MongoDB 7+
- Composer
- Nginx
- SSL证书（生产环境）

### 2. 安装依赖

```bash
composer install
```

### 3. 配置环境变量

```bash
# 复制环境变量模板
cp env.example .env

# 生成安全密钥
chmod +x generate_keys.sh
./generate_keys.sh

# 编辑.env文件，配置必要的参数
nano .env
```

### 4. 创建数据库索引

```bash
php -r "
try {
    \$mongo = new MongoDB\Client('mongodb://localhost:27017/ios_tool');
    \$db = \$mongo->selectDatabase('ios_tool');
    
    // 创建索引
    \$db->users->createIndex(['email' => 1], ['unique' => true]);
    \$db->users->createIndex(['username' => 1], ['unique' => true]);
    \$db->activation_codes->createIndex(['code' => 1], ['unique' => true]);
    \$db->activation_codes->createIndex(['status' => 1]);
    \$db->github_accounts->createIndex(['username' => 1], ['unique' => true]);
    \$db->github_accounts->createIndex(['status' => 1]);
    \$db->upload_records->createIndex(['user_id' => 1]);
    \$db->upload_records->createIndex(['status' => 1]);
    \$db->upload_records->createIndex(['created_at' => -1]);
    
    echo '✅ 数据库索引创建成功\n';
} catch (Exception \$e) {
    echo '❌ 数据库索引创建失败: ' . \$e->getMessage() . '\n';
}
"
```

### 5. 创建管理员账号

```bash
php -r "
try {
    \$mongo = new MongoDB\Client('mongodb://localhost:27017/ios_tool');
    \$db = \$mongo->selectDatabase('ios_tool');
    
    // 创建管理员账号
    \$result = \$db->users->insertOne([
        'username' => 'admin',
        'email' => '<EMAIL>',
        'password' => password_hash('your_admin_password', PASSWORD_DEFAULT),
        'role' => 'admin',
        'status' => 'active',
        'created_at' => new MongoDB\BSON\UTCDateTime(),
        'activated_at' => new MongoDB\BSON\UTCDateTime(),
        'last_login' => null
    ]);
    
    echo '✅ 管理员账号创建成功\n';
} catch (Exception \$e) {
    echo '❌ 管理员账号创建失败: ' . \$e->getMessage() . '\n';
}
"
```

### 6. 启动队列处理器

```bash
php bin/queue-processor.php
```

## 🔧 部署到服务器

### 1. 同步代码到服务器

```bash
chmod +x sync_to_server.sh
./sync_to_server.sh
```

### 2. 服务器端配置

```bash
# 登录服务器
ssh root@************

# 进入项目目录
cd /data/www/api.ios.xxyx.cn

# 安装依赖
composer install --no-dev --optimize-autoloader

# 设置权限
chmod -R 755 storage

# 重启服务（如有需要）
# systemctl reload nginx 或 caddy reload
```

### 3. 启动队列处理器

```bash
# 后台运行队列处理器
nohup php bin/queue-processor.php > queue.log 2>&1 &

# 或者使用screen
screen -S queue
php bin/queue-processor.php
# Ctrl+A+D 分离screen
```

## 📋 API接口

### 认证相关
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/activate` - 激活账号
- `GET /api/auth/verify` - 验证token
- `POST /api/auth/refresh` - 刷新token

### 上传相关
- `POST /api/upload/ipa` - 上传IPA文件
- `GET /api/upload/records` - 获取上传记录（管理员可查所有，普通用户查自己的）
- `GET /api/upload/queue` - 获取队列状态

### 管理员接口
- `POST /api/admin/users` - 创建用户
- `GET /api/admin/users` - 获取用户列表
- `POST /api/admin/activation-codes` - 创建激活码
- `GET /api/admin/activation-codes` - 获取激活码列表
- `POST /api/admin/github-accounts` - 添加GitHub账号
- `GET /api/admin/github-accounts` - 获取GitHub账号列表
- `GET /api/admin/stats` - 获取系统统计

### 工作流相关
- `POST /api/workflow/callback` - 工作流回调
- `GET /download/ipa/{recordId}` - 下载IPA文件

## 🔐 安全配置

### JWT_SECRET
用于生成和验证JWT token，必须保密。

### ENCRYPTION_KEY
用于加密敏感数据（如GitHub Token），必须保密。

### 生成密钥
```bash
./generate_keys.sh
```

## 📊 监控和日志

### 查看日志
```bash
# 应用日志
tail -f /data/logs/uploadipa/app.log

# 错误日志
tail -f /data/logs/uploadipa/error.log

# 队列处理器日志
tail -f queue.log
```

### 系统监控
- 访问管理后台查看系统统计
- 监控GitHub Actions剩余时间
- 监控上传队列状态

## 🔄 更新部署

### 代码更新
```bash
# 本地更新代码后
./sync_to_server.sh

# 服务器端
cd /data/www/api.ios.xxyx.cn
composer install --no-dev
chmod -R 755 storage
systemctl reload nginx
```

### 配置更新
```bash
# 更新.env文件后
./sync_to_server.sh

# 服务器端重启PHP-FPM
systemctl restart php8.3-fpm
```

## 🚨 注意事项

1. **密钥安全**: 不要将JWT_SECRET和ENCRYPTION_KEY提交到代码仓库
2. **文件权限**: 确保storage目录权限正确
3. **SSL证书**: 生产环境必须使用HTTPS
4. **备份数据**: 定期备份MongoDB数据和上传文件
5. **监控资源**: 监控服务器资源使用情况

## 🚀 快速部署

### 一键部署（推荐）
```bash
# 配置.env文件后运行
./deploy.sh
```

### 开发工作流
```bash
# 后端开发
./quick_sync.sh          # 快速同步后端
./sync_to_server.sh      # 完整同步后端

# 前端开发
./sync_frontend.sh       # 基础前端同步
./sync_frontend_advanced.sh -f  # 高级前端同步
```

### 访问地址
- **前端**: https://ios.xxyx.cn
- **API**: https://api.ios.xxyx.cn
- **管理员**: admin / admin123456 ⚠️ 请登录后立即修改密码！

## 🔧 故障排除

```bash
# MongoDB连接失败
sudo systemctl restart mongod

# PHP扩展问题
sudo apt install --reinstall php8.3-mongodb

# 权限问题
sudo chmod -R 755 /data/storage
sudo chown -R caddy:caddy /data/storage

# 服务重启
sudo systemctl restart php8.3-fpm caddy mongod

# 查看日志
sudo journalctl -u caddy -f
tail -f /data/logs/uploadipa/*.log
```

## 📞 技术支持

如有问题，请检查：
1. 日志文件中的错误信息
2. 环境变量配置是否正确
3. 数据库连接是否正常
4. 队列处理器是否正常运行

## �� 许可证

本项目仅供学习和研究使用。

---

**🎯 快速开始**: 运行 `./deploy.sh` 即可完成所有配置！