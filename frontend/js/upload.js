// 上传中心JavaScript逻辑

let currentFile = null;
let uploadInProgress = false;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeUpload();
});

// 初始化上传功能
function initializeUpload() {
    // 认证方式切换
    const authRadios = document.querySelectorAll('input[name="auth-method"]');
    authRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            toggleAuthConfig(this.value);
            checkFormValidity();
        });
    });

    // 文件上传处理
    const fileInput = document.getElementById('ipa-file');
    const fileUploadArea = document.getElementById('file-upload-area');
    
    fileInput.addEventListener('change', handleFileSelect);
    
    // 拖拽上传
    fileUploadArea.addEventListener('dragover', handleDragOver);
    fileUploadArea.addEventListener('dragleave', handleDragLeave);
    fileUploadArea.addEventListener('drop', handleFileDrop);

    // 表单验证
    const inputs = document.querySelectorAll('#step-auth input, #step-auth textarea');
    inputs.forEach(input => {
        input.addEventListener('input', checkFormValidity);
    });

    // 初始化HTTP轮询
    initWebSocket();

    // 刷新进度按钮点击事件
    const refreshBtn = document.getElementById('refresh-progress-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', manualRefreshProgress);
    }
}

// 切换认证配置显示
function toggleAuthConfig(authMethod) {
    const apiKeyConfig = document.getElementById('api-key-config');
    const appleIdConfig = document.getElementById('apple-id-config');
    
    if (authMethod === 'api_key') {
        apiKeyConfig.style.display = 'block';
        appleIdConfig.style.display = 'none';
    } else {
        apiKeyConfig.style.display = 'none';
        appleIdConfig.style.display = 'block';
    }
}

// 处理文件选择
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        processFile(file);
    }
}

// 处理拖拽
function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('dragover');
}

function handleDragLeave(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');
}

function handleFileDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (file.name.endsWith('.ipa')) {
            document.getElementById('ipa-file').files = files;
            processFile(file);
        } else {
            showAlert('请选择.ipa格式的文件', 'error');
        }
    }
}

// 处理文件
function processFile(file) {
    currentFile = file;
    
    // 更新文件上传区域显示
    const uploadPlaceholder = document.querySelector('.upload-placeholder');
    uploadPlaceholder.innerHTML = `
        <div class="upload-icon">✅</div>
        <div class="upload-text">
            <p><strong>${file.name}</strong></p>
            <p>文件大小: ${formatFileSize(file.size)}</p>
            <small>点击重新选择文件</small>
        </div>
    `;
    
    // 解析IPA文件信息
    parseIPAInfo(file);
    
    // 检查表单有效性
    checkFormValidity();
}

// 解析IPA文件信息
async function parseIPAInfo(file) {
    try {
        // 显示加载状态
        const ipaInfo = document.getElementById('ipa-info');
        ipaInfo.style.display = 'block';
        ipaInfo.innerHTML = `
            <h4>📋 应用信息</h4>
            <div class="loading">正在解析IPA文件...</div>
        `;

        // 创建FormData并发送到后端解析
        const formData = new FormData();
        formData.append('ipa_file', file);

        console.log('发送解析请求:', file.name, file.size);

        const response = await fetch(`${API_BASE}/api/upload/parse-ipa`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${getToken()}`
            },
            body: formData
        });

        console.log('解析响应状态:', response.status, response.statusText);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('解析请求失败:', response.status, errorText);
            throw new Error(`解析IPA文件失败 (${response.status}): ${errorText}`);
        }

        const data = await response.json();
        console.log('解析响应数据:', data);

        if (data.success) {
            displayIPAInfo(data.info);
            // 保存文件路径信息，用于后续创建上传任务
            currentFile.serverPath = data.file_path;
            currentFile.serverName = data.file_name;
            currentFile.serverSize = data.file_size;
        } else {
            throw new Error(data.error || '解析失败');
        }
    } catch (error) {
        console.error('解析IPA文件失败:', error);
        document.getElementById('ipa-info').innerHTML = `
            <h4>📋 应用信息</h4>
            <div class="error">解析失败: ${error.message}</div>
        `;
    }
}

// 显示IPA信息
function displayIPAInfo(info) {
    const ipaInfo = document.getElementById('ipa-info');
    ipaInfo.innerHTML = `
        <h4>📋 应用信息</h4>
        <div class="info-grid">
            <div class="info-item">
                <label>应用图标:</label>
                <div id="app-icon" class="app-icon">
                    ${info.icon ? `<img src="${info.icon}" alt="App Icon">` : '📱'}
                </div>
            </div>
            <div class="info-item">
                <label>应用名称:</label>
                <span id="app-name">${info.name || '-'}</span>
            </div>
            <div class="info-item">
                <label>Bundle ID:</label>
                <span id="bundle-id">${info.bundle_id || '-'}</span>
            </div>
            <div class="info-item">
                <label>版本号:</label>
                <span id="app-version">${info.version || '-'}</span>
            </div>
            <div class="info-item">
                <label>构建号:</label>
                <span id="build-number">${info.build || '-'}</span>
            </div>
            <div class="info-item">
                <label>文件大小:</label>
                <span id="file-size">${formatFileSize(currentFile.size)}</span>
            </div>
        </div>
    `;
}

// 检查表单有效性
function checkFormValidity() {
    const authMethod = document.querySelector('input[name="auth-method"]:checked').value;
    let isValid = false;
    
    if (authMethod === 'api_key') {
        const apiKeyId = document.getElementById('api-key-id').value.trim();
        const issuerId = document.getElementById('issuer-id').value.trim();
        const apiKeyContent = document.getElementById('api-key-content').value.trim();
        isValid = apiKeyId && issuerId && apiKeyContent;
    } else {
        const appleId = document.getElementById('apple-id').value.trim();
        const appPassword = document.getElementById('app-password').value.trim();
        isValid = appleId && appPassword;
    }
    
    // 还需要有文件
    isValid = isValid && currentFile;
    
    document.getElementById('upload-btn').disabled = !isValid;
}

// 验证API Key
function validateAPIKey() {
    const apiKeyId = document.getElementById('api-key-id').value.trim();
    const issuerId = document.getElementById('issuer-id').value.trim();
    const apiKeyContent = document.getElementById('api-key-content').value.trim();
    const resultDiv = document.getElementById('api-key-validation-result');

    const errors = [];

    // 验证API Key ID格式
    if (!apiKeyId) {
        errors.push('API Key ID 不能为空');
    } else if (!/^[A-Z0-9]{10}$/.test(apiKeyId)) {
        errors.push('API Key ID 格式错误，应该是10个大写字母和数字');
    }

    // 验证Issuer ID格式
    if (!issuerId) {
        errors.push('Issuer ID 不能为空');
    } else if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(issuerId)) {
        errors.push('Issuer ID 格式错误，应该是UUID格式');
    }

    // 验证私钥内容
    if (!apiKeyContent) {
        errors.push('API Key 私钥内容不能为空');
    } else {
        const lines = apiKeyContent.trim().split('\n');
        if (!lines[0].includes('-----BEGIN PRIVATE KEY-----')) {
            errors.push('私钥缺少开始标记');
        }
        if (!lines[lines.length - 1].includes('-----END PRIVATE KEY-----')) {
            errors.push('私钥缺少结束标记');
        }
    }

    // 显示验证结果
    if (errors.length === 0) {
        resultDiv.innerHTML = '<small style="color: green;">✅ API Key 格式验证通过</small>';
        resultDiv.className = 'validation-success';
    } else {
        resultDiv.innerHTML = '<small style="color: red;">❌ 错误：<br>' + errors.map(e => '• ' + e).join('<br>') + '</small>';
        resultDiv.className = 'validation-error';
    }
}

// 验证Apple ID
function validateAppleID() {
    const appleId = document.getElementById('apple-id').value.trim();
    const appPassword = document.getElementById('app-password').value.trim();
    const resultDiv = document.getElementById('apple-id-validation-result');

    const errors = [];

    // 验证Apple ID格式
    if (!appleId) {
        errors.push('Apple ID 不能为空');
    } else if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(appleId)) {
        errors.push('Apple ID 格式错误，应该是有效的邮箱地址');
    }

    // 验证专属密码格式
    if (!appPassword) {
        errors.push('专属密码不能为空');
    } else if (!/^[a-z]{4}-[a-z]{4}-[a-z]{4}-[a-z]{4}$/.test(appPassword)) {
        errors.push('专属密码格式错误，应该是xxxx-xxxx-xxxx-xxxx格式');
    }

    // 显示验证结果
    if (errors.length === 0) {
        resultDiv.innerHTML = '<small style="color: green;">✅ Apple ID 信息验证通过</small>';
        resultDiv.className = 'validation-success';
    } else {
        resultDiv.innerHTML = '<small style="color: red;">❌ 错误：<br>' + errors.map(e => '• ' + e).join('<br>') + '</small>';
        resultDiv.className = 'validation-error';
    }
}

// 开始上传
async function startUpload() {
    if (uploadInProgress) {
        return;
    }

    uploadInProgress = true;

    // 显示进度界面
    document.getElementById('upload-progress').style.display = 'block';
    document.getElementById('upload-result').style.display = 'none';
    document.getElementById('upload-btn').disabled = true;

    // 重置进度
    updateProgress(0);
    updateStep('step-validate', 'active');
    updateStatus('验证认证信息...');

    try {
        // 收集上传数据
        const uploadData = collectUploadData();

        updateProgress(20);
        updateStep('step-validate', 'completed');
        updateStep('step-github', 'active');
        updateStatus('创建上传任务...');

        // 创建上传任务
        const response = await fetch(`${API_BASE}/api/upload/ipa`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${getToken()}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(uploadData)
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('HTTP Error:', response.status, errorText);
            throw new Error(`创建上传任务失败 (${response.status})`);
        }

        const result = await response.json();
        console.log('Upload API response:', result);

        if (result.success) {
            const uploadId = typeof result.upload_id === 'string' ? result.upload_id : (result.upload_id && (result.upload_id.$oid || result.upload_id));
            console.log('上传任务创建成功，ID:', uploadId);

            // 开始HTTP轮询进度更新
            listenForUploadProgress(uploadId);
            updateStatus('等待处理...');
        } else {
            throw new Error(result.error || '创建上传任务失败');
        }

    } catch (error) {
        console.error('上传失败:', error);
        showUploadError(error.message);
        uploadInProgress = false;
        document.getElementById('upload-btn').disabled = false;
    }
}

// 注意：实际的文件上传将在后续的GitHub工作流中处理
// 这里只是创建上传任务记录


// 显示演示结果
function showDemoResult() {
    const uploadResult = document.getElementById('upload-result');
    const resultContent = document.getElementById('result-content');

    resultContent.innerHTML = `
        <div class="result-success">
            <h4>🎭 演示完成</h4>
            <p>这是上传流程的演示版本。</p>
            <p>实际上传需要：</p>
            <ul style="text-align: left; margin: 10px 0;">
                <li>✅ HTTP轮询（实时进度更新）</li>
                <li>✅ GitHub账号池管理</li>
                <li>✅ 后台工作流处理</li>
                <li>✅ 真实的TestFlight上传</li>
            </ul>
            <div class="result-actions">
                <button class="btn btn-primary" onclick="resetUpload()">重新演示</button>
                <button class="btn btn-secondary" onclick="showTab('records')">查看上传记录</button>
            </div>
        </div>
    `;

    uploadResult.style.display = 'block';
}

// 收集上传数据
function collectUploadData() {
    const authMethod = document.querySelector('input[name="auth-method"]:checked').value;
    const releaseNotes = document.getElementById('release-notes').value.trim();

    // 检查是否有文件
    if (!currentFile) {
        throw new Error('请先选择IPA文件');
    }

    const data = {
        auth_method: authMethod,
        release_notes: releaseNotes,
        file_name: currentFile.name,
        file_size: currentFile.size
    };

    // 如果有服务器端文件路径，添加到数据中
    if (currentFile.serverPath) {
        data.file_path = currentFile.serverPath;
    }

    if (authMethod === 'api_key') {
        data.api_key_id = document.getElementById('api-key-id').value.trim();
        data.issuer_id = document.getElementById('issuer-id').value.trim();
        data.api_key_content = document.getElementById('api-key-content').value.trim();

        // 验证API Key字段
        if (!data.api_key_id || !data.issuer_id || !data.api_key_content) {
            throw new Error('请填写完整的API Key信息');
        }
    } else {
        data.apple_id = document.getElementById('apple-id').value.trim();
        data.app_password = document.getElementById('app-password').value.trim();

        // 验证Apple ID字段
        if (!data.apple_id || !data.app_password) {
            throw new Error('请填写完整的Apple ID信息');
        }
    }

    return data;
}

// 工具函数
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showAlert(message, type = 'info') {
    console.log(`${type.toUpperCase()}: ${message}`);

    // 创建提示框元素
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 6px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        max-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideInRight 0.3s ease-out;
    `;

    // 根据类型设置背景色
    const colors = {
        'success': '#10b981',
        'error': '#ef4444',
        'warning': '#f59e0b',
        'info': '#3b82f6'
    };
    alertDiv.style.backgroundColor = colors[type] || colors.info;

    alertDiv.textContent = message;

    // 添加到页面
    document.body.appendChild(alertDiv);

    // 3秒后自动移除
    setTimeout(() => {
        alertDiv.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 300);
    }, 3000);
}

// 手动刷新模式 - 不使用轮询
let currentUploadId = null;

function initWebSocket() {
    console.log('使用手动刷新模式，不进行自动轮询');
}

// 开始监听上传进度（手动刷新模式）
function listenForUploadProgress(uploadId) {
    currentUploadId = uploadId;
    console.log('开始监听上传进度（手动刷新模式）:', uploadId);

    // 立即查询一次初始状态
    pollUploadProgress();
}

// 手动刷新当前上传任务的进度
async function manualRefreshProgress() {
    if (!currentUploadId) {
        console.warn('没有可刷新的上传任务');
        showAlert('没有正在进行的上传任务', 'warning');
        return;
    }

    console.log('手动刷新进度:', currentUploadId);

    // 显示刷新状态
    const refreshBtn = document.getElementById('refresh-progress-btn');
    if (!refreshBtn) {
        console.error('刷新按钮未找到');
        showAlert('刷新按钮未找到', 'error');
        return;
    }

    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '🔄 刷新中...';
    refreshBtn.disabled = true;

    try {
        await pollUploadProgress();
        showAlert('进度已刷新', 'success');
    } catch (error) {
        console.error('刷新失败:', error);
        showAlert('刷新失败，请稍后重试', 'error');
    } finally {
        // 恢复按钮状态
        refreshBtn.innerHTML = originalText;
        refreshBtn.disabled = false;
    }
}

// 停止监听进度
function stopProgressPolling() {
    currentUploadId = null;
    console.log('停止监听上传进度');
}

// 获取上传进度（手动刷新）
async function pollUploadProgress() {
    if (!currentUploadId) return;

    try {
        const response = await fetch(`${API_BASE}/api/upload/progress/${currentUploadId}`, {
            headers: {
                'Authorization': `Bearer ${getToken()}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            console.log('获取进度数据:', data);

            if (data.success && data.progress) {
                updateUploadProgress(data.progress);

                // 如果上传完成或失败，清除当前任务ID
                if (data.progress.status === 'completed' || data.progress.status === 'failed') {
                    // 延迟清除，让用户能看到最终状态
                    setTimeout(() => {
                        stopProgressPolling();
                    }, 2000);
                }
            } else {
                console.warn('获取进度失败:', data);
                showAlert('获取进度失败，请稍后重试', 'error');
            }
        } else {
            console.error('请求失败:', response.status);
            showAlert('网络请求失败，请检查网络连接', 'error');
        }
    } catch (error) {
        console.error('获取进度失败:', error);
        showAlert('获取进度失败，请稍后重试', 'error');
    }
}

// 更新上传进度
function updateUploadProgress(data) {
    const { step, progress, status, error, message } = data;

    // 更新进度条
    if (typeof progress === 'number') {
        updateProgress(progress);
    }

    // 更新状态文本（优先使用后端提供的 message）
    updateStatus(message || status || '');

    // 更新步骤状态
    if (step) {
        const steps = ['step-validate', 'step-github', 'step-workflow', 'step-upload-file', 'step-testflight'];
        steps.forEach(stepId => {
            const stepElement = document.getElementById(stepId);
            if (stepElement) stepElement.classList.remove('active', 'completed', 'error');
        });

        const currentStepIndex = steps.indexOf(step);
        if (currentStepIndex >= 0) {
            for (let i = 0; i < currentStepIndex; i++) {
                updateStep(steps[i], 'completed');
            }

            if (error) {
                updateStep(step, 'error');
            } else if (status === 'completed' || progress === 100) {
                updateStep(step, 'completed');
            } else {
                updateStep(step, 'active');
            }
        }
    }

    // 完成或错误处理
    if (status === 'completed' || progress === 100) {
        updateProgress(100);
        updateStep('step-testflight', 'completed');
        updateStatus('上传完成！');
        showUploadSuccess(data);
        uploadInProgress = false;
        document.getElementById('upload-btn').disabled = false;
        stopProgressPolling();
    } else if (status === 'failed' || error) {
        showUploadError(error || message || '上传失败');
        uploadInProgress = false;
        document.getElementById('upload-btn').disabled = false;
        stopProgressPolling();
    }
}

// 更新进度条
function updateProgress(percentage) {
    const progressFill = document.getElementById('progress-fill');
    const progressPercentage = document.getElementById('progress-percentage');

    progressFill.style.width = percentage + '%';
    progressPercentage.textContent = percentage + '%';
}

// 更新步骤状态
function updateStep(stepId, status) {
    const stepElement = document.getElementById(stepId);
    if (stepElement) {
        stepElement.classList.remove('active', 'completed', 'error');
        stepElement.classList.add(status);

        const stepIcon = stepElement.querySelector('.step-icon');
        if (stepIcon) {
            switch (status) {
                case 'active':
                    stepIcon.textContent = '⏳';
                    break;
                case 'completed':
                    stepIcon.textContent = '✅';
                    break;
                case 'error':
                    stepIcon.textContent = '❌';
                    break;
            }
        }
    }
}

// 更新状态文本
function updateStatus(status) {
    const statusText = document.getElementById('status-text');
    if (statusText) {
        statusText.textContent = status;
    }
}

// 显示上传错误
function showUploadError(error) {
    const uploadResult = document.getElementById('upload-result');
    const resultContent = document.getElementById('result-content');

    resultContent.innerHTML = `
        <div class="result-error">
            <h4>❌ 上传失败</h4>
            <p>${error}</p>
            <div class="result-actions">
                <button class="btn btn-primary" onclick="retryUpload()">重试上传</button>
                <button class="btn btn-secondary" onclick="resetUpload()">重新开始</button>
            </div>
        </div>
    `;

    uploadResult.style.display = 'block';
}

// 显示上传成功
function showUploadSuccess(data) {
    const uploadResult = document.getElementById('upload-result');
    const resultContent = document.getElementById('result-content');

    resultContent.innerHTML = `
        <div class="result-success">
            <h4>🎉 上传成功</h4>
            <p>您的应用已成功上传到TestFlight！</p>
            ${data.github_url ? `
                <p>
                    <a href="${data.github_url}" target="_blank" class="btn btn-link">
                        📱 查看GitHub Actions进度
                    </a>
                </p>
            ` : ''}
            <div class="result-actions">
                <button class="btn btn-primary" onclick="resetUpload()">上传新应用</button>
                <button class="btn btn-secondary" onclick="showTab('records')">查看上传记录</button>
            </div>
        </div>
    `;

    uploadResult.style.display = 'block';
}

// 重试上传
function retryUpload() {
    stopProgressPolling();
    document.getElementById('upload-result').style.display = 'none';
    startUpload();
}

// 重置上传
function resetUpload() {
    stopProgressPolling();
    uploadInProgress = false;
    currentFile = null;

    // 重置界面
    document.getElementById('upload-progress').style.display = 'none';
    document.getElementById('upload-result').style.display = 'none';
    document.getElementById('ipa-info').style.display = 'none';

    // 重置文件上传区域
    const uploadPlaceholder = document.querySelector('.upload-placeholder');
    uploadPlaceholder.innerHTML = `
        <div class="upload-icon">📱</div>
        <div class="upload-text">
            <p><strong>点击选择IPA文件</strong></p>
            <p>或拖拽文件到此处</p>
            <small>支持.ipa格式，最大500MB</small>
        </div>
    `;

    // 重置表单
    document.getElementById('ipa-file').value = '';
    document.getElementById('release-notes').value = '';

    // 检查表单有效性
    checkFormValidity();
}
