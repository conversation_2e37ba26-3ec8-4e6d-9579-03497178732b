// 管理员后台JavaScript

const API_BASE = 'https://api.ios.xxyx.cn';

// 获取存储的token
function getToken() {
    return localStorage.getItem('token');
}

// 检查登录状态
async function checkAuth() {
    const token = getToken();
    if (!token) {
        alert('请先登录');
        window.location.href = '/login.html';
        return false;
    }

    // 验证token有效性
    try {
        const response = await fetch(`${API_BASE}/api/auth/verify`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            alert('登录已过期，请重新登录');
            window.location.href = '/login.html';
            return false;
        }

        const result = await response.json();
        if (!result.success || result.user.role !== 'admin') {
            alert('需要管理员权限');
            window.location.href = '/index.html';
            return false;
        }

        return true;
    } catch (error) {
        console.error('认证检查失败:', error);
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        alert('认证检查失败，请重新登录');
        window.location.href = '/login.html';
        return false;
    }
}

// API请求函数
async function apiRequest(endpoint, options = {}) {
    const token = getToken();
    if (!token) {
        throw new Error('未登录');
    }

    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        }
    };

    const response = await fetch(`${API_BASE}${endpoint}`, {
        ...defaultOptions,
        ...options
    });

    // 如果是401错误，说明token失效
    if (response.status === 401) {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        alert('登录已过期，请重新登录');
        window.location.href = '/login.html';
        return;
    }

    if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || '请求失败');
    }

    return response.json();
}

// 显示提示信息
function showAlert(containerId, message, type = 'success') {
    const container = document.getElementById(containerId);
    container.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// 切换标签页
function showTab(tabName, event) {
    // 隐藏所有内容
    document.querySelectorAll('.section').forEach(section => {
        section.classList.remove('active');
    });

    // 移除所有标签的active类
    document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // 显示选中的内容
    document.getElementById(tabName).classList.add('active');

    // 添加选中标签的active类
    if (event && event.target) {
        event.target.classList.add('active');
    } else {
        // 如果没有event，通过tabName找到对应的标签
        document.querySelector(`[onclick*="showTab('${tabName}')"]`)?.classList.add('active');
    }

    // 加载对应数据
    switch (tabName) {
        case 'stats':
            loadStats();
            break;
        case 'users':
            loadUsers();
            break;
        case 'activation-codes':
            loadActivationCodes();
            break;
        case 'github-accounts':
            loadGitHubAccounts();
            break;
    }
}

// 加载系统统计
async function loadStats() {
    try {
        const stats = await apiRequest('/api/admin/stats');
        
        const statsGrid = document.getElementById('stats-grid');
        statsGrid.innerHTML = `
            <div class="stat-card">
                <div class="stat-number">${stats.stats.total_users}</div>
                <div class="stat-label">总用户数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.stats.active_users}</div>
                <div class="stat-label">活跃用户</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.stats.total_activation_codes}</div>
                <div class="stat-label">激活码总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.stats.active_activation_codes}</div>
                <div class="stat-label">有效激活码</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.stats.total_github_accounts}</div>
                <div class="stat-label">GitHub账号</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.stats.active_github_accounts}</div>
                <div class="stat-label">活跃账号</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.stats.total_uploads}</div>
                <div class="stat-label">总上传数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.stats.queued_uploads}</div>
                <div class="stat-label">排队中</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.stats.uploading_uploads}</div>
                <div class="stat-label">上传中</div>
            </div>
        `;
    } catch (error) {
        document.getElementById('stats-grid').innerHTML = `<div class="alert alert-error">加载统计信息失败: ${error.message}</div>`;
    }
}

// 加载用户列表
async function loadUsers() {
    try {
        const response = await apiRequest('/api/admin/users');
        
        const usersList = document.getElementById('users-list');
        if (response.users.length === 0) {
            usersList.innerHTML = '<p>暂无用户</p>';
            return;
        }

        const table = `
            <table class="table">
                <thead>
                    <tr>
                        <th>用户名</th>
                        <th>邮箱</th>
                        <th>角色</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>最后登录</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${response.users.map(user => `
                        <tr>
                            <td>${user.username}</td>
                            <td>${user.email}</td>
                            <td>${user.role}</td>
                            <td><span class="status ${user.status}">${user.status}</span></td>
                            <td>${new Date(user.created_at).toLocaleString()}</td>
                            <td>${user.last_login ? new Date(user.last_login).toLocaleString() : '从未登录'}</td>
                            <td>
                                ${user.role !== 'admin' ? `
                                    <button class="btn btn-danger btn-sm" onclick="deleteUser('${user._id}', '${user.username}')">
                                        🗑️ 删除
                                    </button>
                                ` : '<span class="text-muted">管理员</span>'}
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
        
        usersList.innerHTML = table;
    } catch (error) {
        document.getElementById('users-list').innerHTML = `<div class="alert alert-error">加载用户列表失败: ${error.message}</div>`;
    }
}

// 加载激活码列表
async function loadActivationCodes() {
    try {
        const response = await apiRequest('/api/admin/activation-codes');
        
        const codesList = document.getElementById('activation-codes-list');
        if (response.codes.length === 0) {
            codesList.innerHTML = '<p>暂无激活码</p>';
            return;
        }

        const table = `
            <table class="table">
                <thead>
                    <tr>
                        <th>激活码</th>
                        <th>状态</th>
                        <th>已使用次数</th>
                        <th>最大使用次数</th>
                        <th>创建时间</th>
                        <th>过期时间</th>
                    </tr>
                </thead>
                <tbody>
                    ${response.codes.map(code => `
                        <tr>
                            <td><code>${code.code}</code></td>
                            <td><span class="status ${code.status}">${code.status}</span></td>
                            <td>${code.used_count}</td>
                            <td>${code.max_uses}</td>
                            <td>${new Date(code.created_at).toLocaleString()}</td>
                            <td>${new Date(code.expires_at).toLocaleString()}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
        
        codesList.innerHTML = table;
    } catch (error) {
        document.getElementById('activation-codes-list').innerHTML = `<div class="alert alert-error">加载激活码列表失败: ${error.message}</div>`;
    }
}

// 加载GitHub账号列表
async function loadGitHubAccounts() {
    try {
        const response = await apiRequest('/api/admin/github-accounts');

        // 加载统计信息
        loadGitHubStats(response.accounts);

        const accountsList = document.getElementById('github-accounts-list');
        if (response.accounts.length === 0) {
            accountsList.innerHTML = '<p>暂无GitHub账号</p>';
            return;
        }

        const maxUsageLimit = 2500; // 与后端保持一致

        const table = `
            <table class="table">
                <thead>
                    <tr>
                        <th>账号信息</th>
                        <th>使用状态</th>
                        <th>使用情况</th>
                        <th>Token状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${response.accounts.map(account => {
                        const totalUsage = account.total_usage_minutes || 0;
                        const remainingMinutes = maxUsageLimit - totalUsage;
                        const usagePercentage = Math.min((totalUsage / maxUsageLimit) * 100, 100);
                        const isInUse = account.is_in_use;
                        const hasToken = account.has_token !== false; // 假设后端会返回这个字段

                        let statusClass = 'idle';
                        let statusText = '空闲';
                        if (isInUse) {
                            statusClass = 'in-use';
                            statusText = '使用中';
                        } else if (remainingMinutes < 60) {
                            statusClass = 'danger';
                            statusText = '时间不足';
                        } else if (remainingMinutes < 300) {
                            statusClass = 'warning';
                            statusText = '即将用完';
                        }

                        return `
                            <tr>
                                <td>
                                    <strong>${account.username}</strong><br>
                                    <small>${account.email}</small><br>
                                    <span class="status ${account.status}">${account.status}</span>
                                    <br><small>创建: ${new Date(account.created_at).toLocaleDateString()}</small>
                                    ${account.last_used_at ? `<br><small>最后使用: ${new Date(account.last_used_at).toLocaleString()}</small>` : ''}
                                </td>
                                <td>
                                    <span class="status-indicator ${statusClass}"></span>
                                    ${statusText}
                                </td>
                                <td>
                                    <div class="usage-bar">
                                        <div class="usage-fill" style="width: ${usagePercentage}%"></div>
                                    </div>
                                    <div class="usage-text">
                                        已使用: ${totalUsage} / ${maxUsageLimit} 分钟 (${usagePercentage.toFixed(1)}%)
                                        <br>剩余: ${remainingMinutes} 分钟
                                    </div>
                                </td>
                                <td>
                                    ${hasToken ? '✅ 正常' : '❌ 缺失'}
                                </td>
                                <td>
                                    <div class="account-actions">
                                        <button class="btn btn-${account.status === 'active' ? 'danger' : 'success'} btn-sm"
                                                onclick="toggleAccountStatus('${account._id}', '${account.status}')">
                                            ${account.status === 'active' ? '禁用' : '启用'}
                                        </button>
                                        <button class="btn btn-primary btn-sm"
                                                onclick="resetAccountUsage('${account._id}', '${account.username}')">
                                            重置使用
                                        </button>
                                        ${isInUse ? `
                                            <button class="btn btn-warning btn-sm"
                                                    onclick="releaseAccount('${account._id}', '${account.username}')">
                                                释放账号
                                            </button>
                                        ` : ''}
                                    </div>
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        `;

        accountsList.innerHTML = table;
    } catch (error) {
        document.getElementById('github-accounts-list').innerHTML = `<div class="alert alert-error">加载GitHub账号列表失败: ${error.message}</div>`;
    }
}

// 加载GitHub账号统计
function loadGitHubStats(accounts) {
    const maxUsageLimit = 2500;
    const totalAccounts = accounts.length;
    const activeAccounts = accounts.filter(acc => acc.status === 'active').length;
    const inUseAccounts = accounts.filter(acc => acc.is_in_use).length;
    const idleAccounts = activeAccounts - inUseAccounts;

    let totalUsage = 0;
    let warningAccounts = 0;
    let dangerAccounts = 0;

    accounts.forEach(acc => {
        const usage = acc.total_usage_minutes || 0;
        totalUsage += usage;
        const remaining = maxUsageLimit - usage;

        if (remaining < 60) {
            dangerAccounts++;
        } else if (remaining < 300) {
            warningAccounts++;
        }
    });

    const avgUsage = totalAccounts > 0 ? Math.round(totalUsage / totalAccounts) : 0;

    const statsGrid = document.getElementById('github-stats');
    statsGrid.innerHTML = `
        <div class="stat-card">
            <div class="stat-number">${totalAccounts}</div>
            <div class="stat-label">总账号数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${activeAccounts}</div>
            <div class="stat-label">活跃账号</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${idleAccounts}</div>
            <div class="stat-label">空闲账号</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${inUseAccounts}</div>
            <div class="stat-label">使用中</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${avgUsage}</div>
            <div class="stat-label">平均使用(分钟)</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${warningAccounts + dangerAccounts}</div>
            <div class="stat-label">需要关注</div>
        </div>
    `;
}

// 切换GitHub账号状态
async function toggleAccountStatus(accountId, currentStatus) {
    try {
        const newStatus = currentStatus === 'active' ? 'inactive' : 'active';

        await apiRequest(`/api/admin/github-accounts/${accountId}/status`, {
            method: 'PUT',
            body: JSON.stringify({ status: newStatus })
        });

        showAlert('github-alert', '账号状态更新成功');
        loadGitHubAccounts();
    } catch (error) {
        showAlert('github-alert', `更新账号状态失败: ${error.message}`, 'error');
    }
}

// 重置单个账号使用时长
async function resetAccountUsage(accountId, username) {
    if (!confirm(`确定要重置账号 "${username}" 的使用时长吗？`)) {
        return;
    }

    try {
        await apiRequest(`/api/admin/github-accounts/${accountId}/reset-usage`, {
            method: 'POST'
        });

        showAlert('github-alert', `账号 "${username}" 使用时长重置成功`);
        loadGitHubAccounts();
    } catch (error) {
        showAlert('github-alert', `重置使用时长失败: ${error.message}`, 'error');
    }
}

// 释放账号（标记为空闲）
async function releaseAccount(accountId, username) {
    if (!confirm(`确定要释放账号 "${username}" 吗？这将中断当前使用该账号的任务。`)) {
        return;
    }

    try {
        await apiRequest(`/api/admin/github-accounts/${accountId}/release`, {
            method: 'POST'
        });

        showAlert('github-alert', `账号 "${username}" 已释放`);
        loadGitHubAccounts();
    } catch (error) {
        showAlert('github-alert', `释放账号失败: ${error.message}`, 'error');
    }
}

// 刷新所有账号状态
async function refreshAllAccounts() {
    if (!confirm('确定要刷新所有账号状态吗？这可能需要一些时间。')) {
        return;
    }

    try {
        showAlert('github-alert', '正在刷新所有账号状态，请稍候...');

        await apiRequest('/api/admin/github-accounts/refresh-all', {
            method: 'POST'
        });

        showAlert('github-alert', '所有账号状态刷新成功');
        loadGitHubAccounts();
    } catch (error) {
        showAlert('github-alert', `刷新账号状态失败: ${error.message}`, 'error');
    }
}

// 重置所有账号使用时长
async function resetAllUsage() {
    if (!confirm('确定要重置所有账号的使用时长吗？此操作不可撤销！')) {
        return;
    }

    try {
        await apiRequest('/api/admin/github-accounts/reset-all-usage', {
            method: 'POST'
        });

        showAlert('github-alert', '所有账号使用时长重置成功');
        loadGitHubAccounts();
    } catch (error) {
        showAlert('github-alert', `重置所有使用时长失败: ${error.message}`, 'error');
    }
}

// 加载上传记录列表
async function loadUploadRecords(showAll = false) {
    try {
        const endpoint = showAll ? '/api/upload/records?all=1' : '/api/upload/records';
        const response = await apiRequest(endpoint);

        if (!response.success || !response.records) {
            throw new Error('获取上传记录失败');
        }

        const records = response.records;
        const uploadsList = document.getElementById('uploads-list');

        if (records.length === 0) {
            uploadsList.innerHTML = '<div class="alert alert-info">暂无上传记录</div>';
            return;
        }

        const table = `
            <h3>上传记录列表 (${records.length} 条)</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>用户</th>
                        <th>应用信息</th>
                        <th>状态</th>
                        <th>错误信息</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${records.map(record => {
                        const createdAt = new Date(record.created_at?.$date || record.created_at).toLocaleString();
                        const status = record.status || 'unknown';
                        const statusClass = getStatusClass(status);
                        const statusText = getStatusText(status);
                        const error = record.error || '';
                        const bundleId = record.bundle_id || 'N/A';
                        const appName = record.app_name || 'N/A';
                        const version = record.version || 'N/A';
                        const username = record.username || 'N/A';

                        return `
                            <tr>
                                <td>${createdAt}</td>
                                <td>${username}</td>
                                <td>
                                    <div><strong>${appName}</strong></div>
                                    <div style="font-size: 12px; color: #666;">
                                        ${bundleId}<br>
                                        版本: ${version}
                                    </div>
                                </td>
                                <td>
                                    <span class="status-badge ${statusClass}">${statusText}</span>
                                </td>
                                <td>
                                    <div style="max-width: 200px; word-break: break-word; font-size: 12px;">
                                        ${error ? `<span style="color: #dc3545;">${error}</span>` : '-'}
                                    </div>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-primary" onclick="viewUploadDetail('${record._id?.$oid || record._id}')">详情</button>
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        `;

        uploadsList.innerHTML = table;
    } catch (error) {
        document.getElementById('uploads-list').innerHTML = `<div class="alert alert-error">加载上传记录失败: ${error.message}</div>`;
    }
}

// 获取状态样式类
function getStatusClass(status) {
    switch (status) {
        case 'completed': return 'status-success';
        case 'failed': return 'status-error';
        case 'processing': return 'status-warning';
        case 'pending': return 'status-info';
        default: return 'status-secondary';
    }
}

// 获取状态文本
function getStatusText(status) {
    switch (status) {
        case 'completed': return '✅ 完成';
        case 'failed': return '❌ 失败';
        case 'processing': return '🔄 处理中';
        case 'pending': return '⏳ 等待中';
        default: return '❓ 未知';
    }
}

// 查看上传详情
async function viewUploadDetail(recordId) {
    try {
        const response = await apiRequest(`/api/upload/records/${recordId}`);

        if (!response.success || !response.record) {
            throw new Error('获取记录详情失败');
        }

        const record = response.record;
        const createdAt = new Date(record.created_at?.$date || record.created_at).toLocaleString();
        const updatedAt = record.updated_at ? new Date(record.updated_at?.$date || record.updated_at).toLocaleString() : 'N/A';

        const detailHtml = `
            <div style="max-width: 600px; margin: 20px auto; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h3>上传记录详情</h3>
                <div style="margin: 15px 0;">
                    <strong>记录ID:</strong> ${record._id?.$oid || record._id}
                </div>
                <div style="margin: 15px 0;">
                    <strong>创建时间:</strong> ${createdAt}
                </div>
                <div style="margin: 15px 0;">
                    <strong>更新时间:</strong> ${updatedAt}
                </div>
                <div style="margin: 15px 0;">
                    <strong>状态:</strong> <span class="status-badge ${getStatusClass(record.status)}">${getStatusText(record.status)}</span>
                </div>
                <div style="margin: 15px 0;">
                    <strong>应用名称:</strong> ${record.app_name || 'N/A'}
                </div>
                <div style="margin: 15px 0;">
                    <strong>Bundle ID:</strong> ${record.bundle_id || 'N/A'}
                </div>
                <div style="margin: 15px 0;">
                    <strong>版本号:</strong> ${record.version || 'N/A'}
                </div>
                <div style="margin: 15px 0;">
                    <strong>构建号:</strong> ${record.build || 'N/A'}
                </div>
                ${record.error ? `
                <div style="margin: 15px 0;">
                    <strong>错误信息:</strong>
                    <div style="background: #f8f9fa; padding: 10px; border-left: 4px solid #dc3545; margin-top: 5px; word-break: break-word;">
                        ${record.error}
                    </div>
                </div>
                ` : ''}
                <div style="margin-top: 20px; text-align: center;">
                    <button class="btn btn-secondary" onclick="closeModal()">关闭</button>
                </div>
            </div>
        `;

        // 创建模态框
        const modal = document.createElement('div');
        modal.id = 'detail-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        modal.innerHTML = detailHtml;
        modal.onclick = (e) => {
            if (e.target === modal) closeModal();
        };

        document.body.appendChild(modal);
    } catch (error) {
        showAlert('upload-alert', `查看详情失败: ${error.message}`, 'error');
    }
}

// 关闭模态框
function closeModal() {
    const modal = document.getElementById('detail-modal');
    if (modal) {
        modal.remove();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    if (!(await checkAuth())) {
        return;
    }

    // 加载初始数据
    loadStats();
    loadUploadRecords();
    loadUsers();
    loadActivationCodes();
    loadGitHubAccounts();

    // 绑定表单事件
    document.getElementById('create-user-form').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        try {
            const formData = new FormData(this);
            const data = {
                username: formData.get('username'),
                email: formData.get('email'),
                password: formData.get('password'),
                role: formData.get('role')
            };

            await apiRequest('/api/admin/users', {
                method: 'POST',
                body: JSON.stringify(data)
            });

            showAlert('user-alert', '用户创建成功');
            this.reset();
            loadUsers();
        } catch (error) {
            showAlert('user-alert', `创建用户失败: ${error.message}`, 'error');
        }
    });

    document.getElementById('create-activation-form').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        try {
            const formData = new FormData(this);
            const data = {
                expires_in: parseInt(formData.get('expires_in')),
                max_uses: parseInt(formData.get('max_uses'))
            };

            const response = await apiRequest('/api/admin/activation-codes', {
                method: 'POST',
                body: JSON.stringify(data)
            });

            showAlert('activation-alert', `激活码创建成功: ${response.code}`);
            this.reset();
            loadActivationCodes();
        } catch (error) {
            showAlert('activation-alert', `创建激活码失败: ${error.message}`, 'error');
        }
    });

    document.getElementById('add-github-form').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        try {
            const formData = new FormData(this);
            const data = {
                username: formData.get('username'),
                email: formData.get('email'),
                token: formData.get('token')
            };

            await apiRequest('/api/admin/github-accounts', {
                method: 'POST',
                body: JSON.stringify(data)
            });

            showAlert('github-alert', 'GitHub账号添加成功');
            this.reset();
            loadGitHubAccounts();
        } catch (error) {
            showAlert('github-alert', `添加GitHub账号失败: ${error.message}`, 'error');
        }
    });
});

// 删除用户
async function deleteUser(userId, username) {
    if (!confirm(`确定要删除用户 "${username}" 吗？此操作不可撤销！`)) {
        return;
    }

    try {
        await apiRequest(`/api/admin/users/${userId}`, {
            method: 'DELETE'
        });

        showAlert('user-alert', `用户 "${username}" 删除成功`);
        loadUsers(); // 重新加载用户列表
    } catch (error) {
        showAlert('user-alert', `删除用户失败: ${error.message}`, 'error');
    }
}