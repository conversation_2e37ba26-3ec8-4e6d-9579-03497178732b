<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闲闲iOS工具 - 管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .tabs {
            display: flex;
            background: #fff;
            border-radius: 8px 8px 0 0;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .tab {
            flex: 1;
            padding: 15px 20px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
            font-size: 14px;
            font-weight: 500;
        }

        .tab.active {
            background: #007bff;
            color: white;
        }

        .tab:hover:not(.active) {
            background: #e9ecef;
        }

        .content {
            background: #fff;
            padding: 20px;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            min-height: 400px;
        }

        .section {
            display: none;
        }

        .section.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #495057;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group textarea {
            height: 100px;
            resize: vertical;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background 0.3s;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #1e7e34;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
        }

        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status.active {
            background: #d4edda;
            color: #155724;
        }

        .status.inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 14px;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .usage-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 5px 0;
        }

        .usage-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
            transition: width 0.3s ease;
        }

        .usage-text {
            font-size: 12px;
            color: #6c757d;
            margin-top: 2px;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
            margin: 2px;
        }

        .account-actions {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-indicator.idle {
            background: #28a745;
        }

        .status-indicator.in-use {
            background: #ffc107;
        }

        .status-indicator.warning {
            background: #fd7e14;
        }

        .status-indicator.danger {
            background: #dc3545;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status-secondary {
            background: #e2e3e5;
            color: #383d41;
            border: 1px solid #d6d8db;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }

        .alert-info {
            color: #31708f;
            background-color: #d9edf7;
            border-color: #bce8f1;
        }

        .alert-error {
            color: #a94442;
            background-color: #f2dede;
            border-color: #ebccd1;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>闲闲iOS工具 - 管理后台</h1>
            <p>系统管理和监控</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showTab('stats', event)">系统统计</button>
            <button class="tab" onclick="showTab('uploads', event)">上传记录</button>
            <button class="tab" onclick="showTab('users', event)">用户管理</button>
            <button class="tab" onclick="showTab('activation-codes', event)">激活码管理</button>
            <button class="tab" onclick="showTab('github-accounts', event)">GitHub账号</button>
        </div>

        <div class="content">
            <!-- 系统统计 -->
            <div id="stats" class="section active">
                <h2>系统统计</h2>
                <div id="stats-grid" class="stats-grid">
                    <div class="loading">加载中...</div>
                </div>
            </div>

            <!-- 上传记录 -->
            <div id="uploads" class="section">
                <h2>上传记录管理</h2>
                <div id="upload-alert"></div>

                <div style="margin-bottom: 20px;">
                    <button class="btn btn-primary" onclick="loadUploadRecords()">刷新记录</button>
                    <button class="btn btn-success" onclick="loadUploadRecords(true)">显示所有用户</button>
                </div>

                <div id="uploads-list">
                    <h3>上传记录列表</h3>
                    <div class="loading">加载中...</div>
                </div>
            </div>

            <!-- 用户管理 -->
            <div id="users" class="section">
                <h2>用户管理</h2>
                <div id="user-alert"></div>

                <form id="create-user-form">
                    <h3>创建新用户</h3>
                    <div class="form-group">
                        <label>用户名</label>
                        <input type="text" name="username" required>
                    </div>
                    <div class="form-group">
                        <label>邮箱</label>
                        <input type="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label>密码</label>
                        <input type="password" name="password" required>
                    </div>
                    <div class="form-group">
                        <label>角色</label>
                        <select name="role">
                            <option value="user">普通用户</option>
                            <option value="admin">管理员</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">创建用户</button>
                </form>

                <div id="users-list">
                    <h3>用户列表</h3>
                    <div class="loading">加载中...</div>
                </div>
            </div>

            <!-- 激活码管理 -->
            <div id="activation-codes" class="section">
                <h2>激活码管理</h2>
                <div id="activation-alert"></div>

                <form id="create-activation-form">
                    <h3>创建激活码</h3>
                    <div class="form-group">
                        <label>有效期（小时）</label>
                        <input type="number" name="expires_in" value="24" min="1" max="720">
                    </div>
                    <div class="form-group">
                        <label>最大使用次数</label>
                        <input type="number" name="max_uses" value="1" min="1" max="100">
                    </div>
                    <button type="submit" class="btn btn-primary">生成激活码</button>
                </form>

                <div id="activation-codes-list">
                    <h3>激活码列表</h3>
                    <div class="loading">加载中...</div>
                </div>
            </div>

            <!-- GitHub账号管理 -->
            <div id="github-accounts" class="section">
                <h2>GitHub账号管理</h2>
                <div id="github-alert"></div>

                <!-- 账号统计 -->
                <div id="github-stats" class="stats-grid" style="margin-bottom: 30px;">
                    <div class="loading">加载统计中...</div>
                </div>

                <!-- 批量操作 -->
                <div style="margin-bottom: 20px;">
                    <button class="btn btn-success" onclick="refreshAllAccounts()">🔄 刷新所有账号状态</button>
                    <button class="btn btn-danger" onclick="resetAllUsage()">🔄 重置所有使用时长</button>
                </div>

                <form id="add-github-form">
                    <h3>添加GitHub账号</h3>
                    <div class="form-group">
                        <label>GitHub用户名</label>
                        <input type="text" name="username" required>
                    </div>
                    <div class="form-group">
                        <label>邮箱</label>
                        <input type="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label>Personal Access Token</label>
                        <textarea name="token" placeholder="请输入GitHub Personal Access Token" required></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">添加账号</button>
                </form>

                <div id="github-accounts-list">
                    <h3>GitHub账号列表</h3>
                    <div class="loading">加载中...</div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/admin.js"></script>
</body>

</html>