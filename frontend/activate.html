<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账号激活 - XIOS</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }

        .activate-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 400px;
        }

        .activate-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .activate-header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .activate-header p {
            color: #7f8c8d;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #2c3e50;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }

        .form-group input:focus {
            outline: none;
            border-color: #007bff;
        }

        .btn {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s;
        }

        .btn:hover:not(:disabled) {
            background: #0056b3;
        }

        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
        }

        .back-link a {
            color: #007bff;
            text-decoration: none;
            font-size: 14px;
        }

        .back-link a:hover {
            text-decoration: underline;
        }

        .activation-info {
            background: #e7f3ff;
            border: 1px solid #b3d7ff;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #0c5460;
        }

        .activation-info h3 {
            margin: 0 0 10px 0;
            color: #0c5460;
        }

        .activation-info ul {
            margin: 10px 0 0 20px;
            padding: 0;
        }
    </style>
</head>

<body>
    <div class="activate-container">
        <div class="activate-header">
            <h1>🔑 账号激活</h1>
            <p>请输入激活码激活您的账号</p>
        </div>

        <div class="activation-info">
            <h3>激活说明:</h3>
            <ul>
                <li>请联系管理员获取激活码</li>
                <li>激活码具有时效性，请及时使用</li>
                <li>激活成功后即可正常使用系统</li>
            </ul>
        </div>

        <div id="alert"></div>
        <div class="loading" id="loading">激活中...</div>

        <form id="activate-form">
            <div class="form-group">
                <label>用户名</label>
                <input type="text" name="username" placeholder="请输入用户名" required>
            </div>
            <div class="form-group">
                <label>激活码</label>
                <input type="text" name="activation_code" placeholder="请输入激活码" required>
            </div>
            <button type="submit" class="btn" id="activate-btn">激活账号</button>
        </form>

        <div class="back-link">
            <a href="login.html">← 返回登录</a>
        </div>
    </div>

    <script>
        const API_BASE = 'https://api.ios.xxyx.cn';

        function showAlert(message, type = 'error') {
            const alertDiv = document.getElementById('alert');
            alertDiv.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
            setTimeout(() => {
                alertDiv.innerHTML = '';
            }, 5000);
        }

        function setLoading(loading) {
            const loadingDiv = document.getElementById('loading');
            const activateBtn = document.getElementById('activate-btn');
            const form = document.getElementById('activate-form');

            if (loading) {
                loadingDiv.style.display = 'block';
                activateBtn.disabled = true;
                form.style.opacity = '0.6';
            } else {
                loadingDiv.style.display = 'none';
                activateBtn.disabled = false;
                form.style.opacity = '1';
            }
        }

        async function activate(username, activationCode) {
            const response = await fetch(`${API_BASE}/api/auth/activate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: username,
                    activation_code: activationCode
                })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || '激活失败');
            }

            return data;
        }

        document.getElementById('activate-form').addEventListener('submit', async function (e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const username = formData.get('username').trim();
            const activationCode = formData.get('activation_code').trim();

            if (!username || !activationCode) {
                showAlert('请填写完整信息');
                return;
            }

            setLoading(true);

            try {
                const result = await activate(username, activationCode);

                if (result.success) {
                    showAlert('账号激活成功！正在跳转到登录页面...', 'success');
                    setTimeout(() => {
                        window.location.href = '/login.html';
                    }, 2000);
                } else {
                    throw new Error(result.error || '激活失败');
                }
            } catch (error) {
                console.error('激活失败:', error);
                showAlert(error.message);
            } finally {
                setLoading(false);
            }
        });

        // 检查是否已经登录
        document.addEventListener('DOMContentLoaded', function () {
            const token = localStorage.getItem('token');
            if (token) {
                // 已登录，跳转到主页
                window.location.href = '/index.html';
                return;
            }

            // 检查是否从登录页面跳转过来，自动填充用户名
            const savedUsername = sessionStorage.getItem('activation_username');
            if (savedUsername) {
                const usernameInput = document.querySelector('input[name="username"]');
                if (usernameInput) {
                    usernameInput.value = savedUsername;
                    // 清除sessionStorage中的用户名
                    sessionStorage.removeItem('activation_username');
                    // 自动聚焦到激活码输入框
                    const activationCodeInput = document.querySelector('input[name="activation_code"]');
                    if (activationCodeInput) {
                        activationCodeInput.focus();
                    }
                }
            }
        });
    </script>
</body>

</html>