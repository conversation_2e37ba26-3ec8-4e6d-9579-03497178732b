/* XIOS - 主样式文件 */

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

/* 容器样式 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 头部样式 */
.header {
    background: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.logo {
    font-size: 24px;
    font-weight: 700;
    color: #007bff;
    text-decoration: none;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
    color: #666;
}

.logout-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s;
}

.logout-btn:hover {
    background: #c82333;
}

/* 标签页样式 */
.tabs {
    background: #fff;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 0;
}

.tabs-container {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.tab {
    padding: 15px 25px;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    border-bottom: 3px solid transparent;
    transition: all 0.3s;
}

.tab:hover {
    color: #007bff;
    background: #f8f9fa;
}

.tab.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: #f8f9fa;
}

/* 内容区域 */
.content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.section {
    display: none;
}

.section.active {
    display: block;
}

/* 卡片样式 */
.card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 20px;
}

.card h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 600;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-control:disabled {
    background: #f8f9fa;
    color: #6c757d;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s;
    line-height: 1;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #0056b3;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #1e7e34;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #c82333;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #545b62;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-block {
    width: 100%;
}

/* 状态样式 */
.status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-processing {
    background: #d1ecf1;
    color: #0c5460;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-failed {
    background: #f8d7da;
    color: #721c24;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

/* 进度条 */
.progress {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-bar {
    height: 100%;
    background: #007bff;
    width: 0%;
    transition: width 0.3s;
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.table tbody tr:hover {
    background: #f8f9fa;
}

/* 列表样式 */
.list-item {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 10px;
    transition: box-shadow 0.3s;
}

.list-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.list-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.list-item-title {
    font-weight: 500;
    color: #333;
}

.list-item-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    font-size: 14px;
    color: #666;
}

/* 工具栏 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 10px;
}

.toolbar-left,
.toolbar-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 40px;
    color: #666;
}

.loading::before {
    content: "⏳";
    margin-right: 8px;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .tabs-container {
        flex-direction: column;
    }
    
    .tab {
        text-align: center;
        border-bottom: 1px solid #e9ecef;
        border-right: none;
    }
    
    .toolbar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .list-item-details {
        grid-template-columns: 1fr;
    }
}

/* 提示框动画 */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}
