# 🔄 XIOS 同步脚本使用指南

## 📋 脚本概览

| 脚本 | 用途 | 依赖处理 | 使用场景 |
|------|------|----------|----------|
| `sync_to_server.sh` | 后端同步 | 可选 | 后端开发/部署 |
| `sync_frontend.sh` | 前端同步 | 无 | 前端更新 |

## 🚀 使用方法

### 1. 后端同步（智能依赖管理）

#### 基础同步（默认）
```bash
./sync_to_server.sh
```
- 同步代码
- 只更新autoload
- 如果vendor不存在会自动安装

#### 安装/更新依赖
```bash
./sync_to_server.sh -i
```
- 同步代码
- 检查并安装/更新依赖
- 智能判断是安装还是更新

#### 强制重新安装依赖
```bash
./sync_to_server.sh -f
```
- 同步代码
- 删除现有vendor和composer.lock
- 重新安装所有依赖

### 2. 前端专用同步

```bash
./sync_frontend.sh -f
```

**特点**：
- ✅ 只同步前端文件
- ✅ 支持备份
- ✅ 自动设置权限
- ✅ 不影响后端

## 🎯 使用场景建议

### 开发阶段
```bash
# 修改PHP代码后（日常开发）
./sync_to_server.sh

# 修改前端代码后
./sync_frontend.sh -f

# 添加新的PHP依赖后
./sync_to_server.sh -i
```

### 部署阶段
```bash
# 首次部署
./sync_to_server.sh -f

# 更新部署
./sync_to_server.sh -i

# 紧急修复
./sync_to_server.sh
```

### 问题排查
```bash
# 依赖问题
./sync_to_server.sh -f

# 代码问题
./sync_to_server.sh

# 前端问题
./sync_frontend.sh -f
```

## ⚠️ 重要注意事项

### 依赖管理
- **不要**在生产环境手动删除vendor目录
- **优先使用** `./sync_to_server.sh` 进行日常同步
- **只在必要时**使用 `-f` 强制重装依赖

### 服务器状态
- 同步前确保服务器可访问
- 检查磁盘空间是否充足
- 确认PHP和Composer版本兼容

### 备份建议
- 重要更新前先备份数据库
- 使用前端同步的备份功能
- 保留关键配置文件副本

## 🔧 故障排除

### 常见问题

1. **依赖冲突**
   ```bash
   ./sync_to_server.sh -f  # 强制重装
   ```

2. **autoload问题**
   ```bash
   ./sync_to_server.sh  # 重新生成autoload
   ```

3. **权限问题**
   ```bash
   ssh root@123.207.8.82 'chown -R caddy:caddy /data/www/api.ios.xxyx.cn'
   ```

4. **服务异常**
   ```bash
   ssh root@123.207.8.82 'systemctl restart php8.3-fpm caddy'
   ```

### 检查命令
```bash
# 检查服务状态
ssh root@123.207.8.82 'systemctl status php8.3-fpm caddy mongod'

# 检查依赖
ssh root@123.207.8.82 'cd /data/www/api.ios.xxyx.cn && composer check-platform-reqs'

# 检查日志
ssh root@123.207.8.82 'tail -f /var/log/php8.3-fpm.log'
```

## 📊 性能对比

| 操作 | quick_sync | sync_to_server | sync_to_server -f |
|------|------------|----------------|-------------------|
| 同步时间 | ~10秒 | ~15秒 | ~60秒 |
| 网络传输 | 最少 | 中等 | 最多 |
| 服务中断 | 最短 | 中等 | 最长 |
| 安全性 | 高 | 高 | 中等 |

## 🎉 最佳实践

1. **开发流程**：
   - 本地测试 → `quick_sync.sh` → 线上验证

2. **版本发布**：
   - 代码审查 → `sync_to_server.sh -i` → 功能测试

3. **紧急修复**：
   - 快速修复 → `quick_sync.sh` → 立即验证

4. **环境重建**：
   - 清理环境 → `sync_to_server.sh -f` → 完整测试

记住：**选择合适的工具完成合适的任务！** 🚀
