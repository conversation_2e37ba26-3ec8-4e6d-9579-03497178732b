<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Services\GitHubService;
use App\Services\WorkflowGenerator;

echo "测试工作流修复...\n";

// 创建 WorkflowGenerator 实例
$workflowGenerator = new WorkflowGenerator();

// 测试数据
$testData = [
    'upload_id' => 'test_upload_123',
    'callback_url' => 'https://api.ios.xxyx.cn/api/upload/workflow-callback',
    'download_url' => 'https://example.com/test.ipa',
    'bundle_id' => 'com.example.testapp',
    'app_name' => 'Test App',
    'version' => '1.0.0',
    'build' => '1',
    'api_key_id' => 'TEST_KEY_ID',
    'issuer_id' => 'TEST_ISSUER_ID',
    'api_key_content' => 'TEST_API_KEY_CONTENT',
    'apple_id' => '<EMAIL>',
    'app_password' => 'test-test-test-test'
];

echo "\n1. 测试高级API Key工作流生成...\n";
try {
    $apiKeyWorkflow = $workflowGenerator->generateAdvancedAPIKeyWorkflow($testData);
    
    // 检查是否包含正确的输入参数引用
    if (strpos($apiKeyWorkflow, '${{ inputs.upload_id }}') !== false) {
        echo "✅ API Key工作流包含正确的输入参数引用\n";
    } else {
        echo "❌ API Key工作流缺少输入参数引用\n";
    }
    
    // 检查工作流结构
    if (strpos($apiKeyWorkflow, 'workflow_dispatch:') !== false && 
        strpos($apiKeyWorkflow, 'upload_id:') !== false) {
        echo "✅ API Key工作流结构正确\n";
    } else {
        echo "❌ API Key工作流结构有问题\n";
    }
    
} catch (Exception $e) {
    echo "❌ API Key工作流生成失败: " . $e->getMessage() . "\n";
}

echo "\n2. 测试高级Apple ID工作流生成...\n";
try {
    $appleIdWorkflow = $workflowGenerator->generateAdvancedAppleIDWorkflow($testData);
    
    // 检查是否包含正确的输入参数引用
    if (strpos($appleIdWorkflow, '${{ inputs.upload_id }}') !== false) {
        echo "✅ Apple ID工作流包含正确的输入参数引用\n";
    } else {
        echo "❌ Apple ID工作流缺少输入参数引用\n";
    }
    
    // 检查工作流结构
    if (strpos($appleIdWorkflow, 'workflow_dispatch:') !== false && 
        strpos($appleIdWorkflow, 'upload_id:') !== false) {
        echo "✅ Apple ID工作流结构正确\n";
    } else {
        echo "❌ Apple ID工作流结构有问题\n";
    }
    
} catch (Exception $e) {
    echo "❌ Apple ID工作流生成失败: " . $e->getMessage() . "\n";
}

echo "\n3. 测试GitHubService集成...\n";
try {
    $githubService = new GitHubService();
    
    // 测试工作流文件名生成
    $workflowFileName = $githubService->generateWorkflowFileName();
    if ($workflowFileName === 'upload-testflight.yml') {
        echo "✅ 工作流文件名生成正确\n";
    } else {
        echo "❌ 工作流文件名生成错误: $workflowFileName\n";
    }
    
    // 测试仓库名生成
    $repoName = $githubService->generateRepoName();
    if (strpos($repoName, 'ios-uploader-') === 0) {
        echo "✅ 仓库名生成正确: $repoName\n";
    } else {
        echo "❌ 仓库名生成错误: $repoName\n";
    }
    
} catch (Exception $e) {
    echo "❌ GitHubService测试失败: " . $e->getMessage() . "\n";
}

echo "\n4. 检查关键修复点...\n";

// 检查输入参数使用
$apiKeyWorkflow = $workflowGenerator->generateAdvancedAPIKeyWorkflow($testData);
$inputUsageCount = substr_count($apiKeyWorkflow, '${{ inputs.upload_id }}');
echo "API Key工作流中输入参数使用次数: $inputUsageCount\n";

$appleIdWorkflow = $workflowGenerator->generateAdvancedAppleIDWorkflow($testData);
$inputUsageCount2 = substr_count($appleIdWorkflow, '${{ inputs.upload_id }}');
echo "Apple ID工作流中输入参数使用次数: $inputUsageCount2\n";

if ($inputUsageCount >= 3 && $inputUsageCount2 >= 3) {
    echo "✅ 输入参数在工作流中被正确使用\n";
} else {
    echo "❌ 输入参数使用不足，可能导致GitHub Actions无法执行\n";
}

echo "\n测试完成！\n";
echo "\n主要修复内容:\n";
echo "1. 修复了工作流中 upload_id 参数的引用问题\n";
echo "2. 将硬编码的 {$uploadId} 替换为 \${{ inputs.upload_id }}\n";
echo "3. 确保GitHub Actions能够正确接收和使用输入参数\n";
echo "4. 保持了向后兼容性，原有功能不受影响\n";
