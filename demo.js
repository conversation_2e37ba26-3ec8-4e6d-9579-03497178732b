// GitHub API 配置
let githubToken = '';
let repoOwner = '';
let repoName = '';
let repoCreated = false;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 认证方式切换
    const authRadios = document.querySelectorAll('input[name="auth-method"]');
    authRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            const apiKeyConfig = document.getElementById('api-key-config');
            const appleIdConfig = document.getElementById('apple-id-config');

            if (this.value === 'api_key') {
                apiKeyConfig.style.display = 'block';
                appleIdConfig.style.display = 'none';
            } else {
                apiKeyConfig.style.display = 'none';
                appleIdConfig.style.display = 'block';
            }
        });
    });
    // 文件上传处理
    const fileInput = document.getElementById('ipa-file');
    const fileLabel = fileInput.nextElementSibling;
    
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            fileLabel.innerHTML = `<i class="fas fa-check-circle"></i> ${file.name}`;
            fileLabel.style.background = '#e8f5e8';
            fileLabel.style.borderColor = '#4caf50';
        }
    });

    // 拖拽上传
    fileLabel.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.style.background = '#f0f4ff';
        this.style.borderColor = '#667eea';
    });

    fileLabel.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.style.background = '#f9f9f9';
        this.style.borderColor = '#ccc';
    });

    fileLabel.addEventListener('drop', function(e) {
        e.preventDefault();
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            fileInput.dispatchEvent(new Event('change'));
        }
    });

});

// 验证 API Key 格式
function validateAPIKey() {
    const apiKeyId = document.getElementById('api-key-id').value;
    const issuerId = document.getElementById('issuer-id').value;
    const apiKeyContent = document.getElementById('api-key-content').value;
    const resultDiv = document.getElementById('api-key-validation-result');

    const errors = [];
    const warnings = [];

    // 验证 API Key ID
    if (!apiKeyId) {
        errors.push('API Key ID 不能为空');
    } else if (!/^[A-Z0-9]{10}$/.test(apiKeyId)) {
        errors.push('API Key ID 格式错误，应该是10个大写字母和数字');
    }

    // 验证 Issuer ID
    if (!issuerId) {
        errors.push('Issuer ID 不能为空');
    } else if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(issuerId)) {
        errors.push('Issuer ID 格式错误，应该是 UUID 格式');
    }

    // 验证私钥内容
    if (!apiKeyContent) {
        errors.push('API Key 私钥内容不能为空');
    } else {
        const lines = apiKeyContent.trim().split('\n');

        // 检查开始和结束标记
        if (!lines[0].includes('-----BEGIN PRIVATE KEY-----')) {
            errors.push('私钥缺少开始标记 "-----BEGIN PRIVATE KEY-----"');
        }
        if (!lines[lines.length - 1].includes('-----END PRIVATE KEY-----')) {
            errors.push('私钥缺少结束标记 "-----END PRIVATE KEY-----"');
        }

        // 检查私钥长度
        if (lines.length < 3) {
            errors.push('私钥内容太短，可能不完整');
        }

        // 检查 base64 内容
        const keyContent = lines.slice(1, -1).join('');
        if (keyContent.length < 50) {
            warnings.push('私钥内容较短，请确认是否完整');
        }

        // 检查是否包含无效字符
        if (!/^[A-Za-z0-9+/=\s]*$/.test(keyContent)) {
            errors.push('私钥内容包含无效字符');
        }
    }

    // 显示结果
    if (errors.length === 0 && warnings.length === 0) {
        resultDiv.innerHTML = '<small style="color: green;">✅ API Key 格式验证通过</small>';
    } else {
        let html = '';
        if (errors.length > 0) {
            html += '<small style="color: red;">❌ 错误：<br>' + errors.map(e => '• ' + e).join('<br>') + '</small>';
        }
        if (warnings.length > 0) {
            html += '<br><small style="color: orange;">⚠️ 警告：<br>' + warnings.map(w => '• ' + w).join('<br>') + '</small>';
        }
        resultDiv.innerHTML = html;
    }
}

// 验证 Apple ID 和专属密码格式
function validateAppleID() {
    const appleId = document.getElementById('apple-id').value;
    const appPassword = document.getElementById('app-password').value;
    const resultDiv = document.getElementById('apple-id-validation-result');

    const errors = [];
    const warnings = [];

    // 验证 Apple ID 格式
    if (!appleId) {
        errors.push('Apple ID 不能为空');
    } else if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(appleId)) {
        errors.push('Apple ID 格式错误，应该是有效的邮箱地址');
    }

    // 验证专属密码格式
    if (!appPassword) {
        errors.push('专属密码不能为空');
    } else if (!/^[a-z]{4}-[a-z]{4}-[a-z]{4}-[a-z]{4}$/.test(appPassword)) {
        errors.push('专属密码格式错误，应该是 xxxx-xxxx-xxxx-xxxx 格式（16个小写字母）');
    } else {
        // 检查是否包含数字或大写字母
        if (/[A-Z0-9]/.test(appPassword)) {
            warnings.push('专属密码通常只包含小写字母，请确认格式正确');
        }
    }

    // 显示结果
    if (errors.length === 0 && warnings.length === 0) {
        resultDiv.innerHTML = '<small style="color: green;">✅ Apple ID 信息验证通过</small>';
    } else {
        let html = '';
        if (errors.length > 0) {
            html += '<small style="color: red;">❌ 错误：<br>' + errors.map(e => '• ' + e).join('<br>') + '</small>';
        }
        if (warnings.length > 0) {
            html += '<br><small style="color: orange;">⚠️ 警告：<br>' + warnings.map(w => '• ' + w).join('<br>') + '</small>';
        }
        resultDiv.innerHTML = html;
    }
}

// 测试 Token 按钮功能
async function testToken() {
    const token = document.getElementById('github-token').value;
    const resultDiv = document.getElementById('token-test-result');

    if (!token) {
        resultDiv.innerHTML = '<small style="color: red;">请先输入 GitHub token</small>';
        return;
    }

    resultDiv.innerHTML = '<small style="color: blue;">正在验证 token...</small>';

    try {
        const user = await testGitHubToken(token);
        resultDiv.innerHTML = `<small style="color: green;">✅ Token 有效！用户: ${user.login}</small>`;

        // 自动填充用户名
        if (!document.getElementById('github-username').value) {
            document.getElementById('github-username').value = user.login;
        }
    } catch (error) {
        resultDiv.innerHTML = `<small style="color: red;">❌ ${error.message}</small>`;
    }
}

// 测试 GitHub Token
async function testGitHubToken(token) {
    try {
        const response = await fetch('https://api.github.com/user', {
            headers: {
                'Authorization': `token ${token}`,
                'Accept': 'application/vnd.github.v3+json'
            }
        });

        if (!response.ok) {
            if (response.status === 401) {
                throw new Error('Token 无效或已过期');
            }
            throw new Error(`Token 验证失败: ${response.statusText}`);
        }

        const user = await response.json();
        console.log('GitHub token validated for user:', user.login);
        return user;
    } catch (error) {
        throw new Error(`Token 验证失败: ${error.message}`);
    }
}

// 自动创建并配置 GitHub Repository
async function setupGitHubRepo() {
    const username = document.getElementById('github-username').value;
    const token = document.getElementById('github-token').value;
    const repoNameInput = document.getElementById('repo-name').value;
    const isPrivate = document.getElementById('private-repo').checked;

    if (!username || !token || !repoNameInput) {
        alert('请填写所有必需的 GitHub 信息');
        return;
    }

    githubToken = token;
    repoOwner = username;
    repoName = repoNameInput;

    const resultDiv = document.getElementById('repo-setup-result');
    resultDiv.innerHTML = '<div class="progress"><div class="progress-bar"><div class="progress-fill"></div></div><p>正在验证 GitHub token...</p></div>';

    try {
        // 步骤 0: 验证 GitHub token
        await testGitHubToken(token);

        // 步骤 1: 创建 repository
        resultDiv.innerHTML = '<div class="progress"><div class="progress-bar"><div class="progress-fill"></div></div><p>正在创建 repository...</p></div>';
        await createRepository(repoNameInput, isPrivate);

        // 步骤 2: 上传工作流文件
        resultDiv.innerHTML = '<div class="progress"><div class="progress-bar"><div class="progress-fill"></div></div><p>正在上传工作流文件...</p></div>';
        await uploadWorkflowFile();

        // 步骤 3: 上传前端文件
        resultDiv.innerHTML = '<div class="progress"><div class="progress-bar"><div class="progress-fill"></div></div><p>正在上传前端文件...</p></div>';
        await uploadFrontendFiles();

        // 步骤 4: 配置 GitHub Secrets (稍后在上传时配置)

        repoCreated = true;
        resultDiv.innerHTML = `
            <div class="alert alert-success">
                <strong>🎉 Repository 创建成功！</strong>
                <br>Repository: <a href="https://github.com/${repoOwner}/${repoName}" target="_blank">
                    https://github.com/${repoOwner}/${repoName}
                </a>
                <br>现在可以配置苹果开发者账号信息并开始上传。
            </div>
        `;

    } catch (error) {
        console.error('Repository setup failed:', error);
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <strong>❌ 创建失败：</strong> ${error.message}
                <br><small>常见问题：</small>
                <br>• 检查 GitHub token 是否有效
                <br>• 确认 token 具有 repo 权限
                <br>• 检查网络连接
                <br>• 确认用户名正确
            </div>
        `;
    }
}

// 检查仓库是否存在
async function checkRepositoryExists(name) {
    try {
        const response = await fetch(`https://api.github.com/repos/${repoOwner}/${name}`, {
            headers: {
                'Authorization': `token ${githubToken}`,
                'Accept': 'application/vnd.github.v3+json'
            }
        });

        return response.ok;
    } catch (error) {
        return false;
    }
}

// 创建 GitHub Repository
async function createRepository(name, isPrivate) {
    console.log(`Checking if repository exists: ${name}`);

    // 首先检查仓库是否已存在
    const exists = await checkRepositoryExists(name);
    if (exists) {
        console.log('Repository already exists, skipping creation');
        return;
    }

    console.log(`Creating new repository: ${name}, private: ${isPrivate}`);

    try {
        const response = await fetch('https://api.github.com/user/repos', {
            method: 'POST',
            headers: {
                'Authorization': `token ${githubToken}`,
                'Content-Type': 'application/json',
                'Accept': 'application/vnd.github.v3+json'
            },
            body: JSON.stringify({
                name: name,
                description: 'iOS App Uploader with GitHub Actions',
                private: isPrivate,
                auto_init: true
            })
        });

        if (!response.ok) {
            const error = await response.json();
            console.error('GitHub API Error:', error);

            if (response.status === 422 && error.errors?.[0]?.message?.includes('already exists')) {
                // Repository 已存在，继续使用
                console.log('Repository already exists (race condition), continuing...');
                return;
            }

            if (response.status === 401) {
                throw new Error('GitHub token 无效或已过期，请检查 token 权限');
            }

            if (response.status === 403) {
                throw new Error('GitHub token 权限不足，需要 repo 权限');
            }

            throw new Error(`创建仓库失败: ${error.message || response.statusText}`);
        }

        const result = await response.json();
        console.log('Repository created successfully:', result.html_url);

        // 等待一下让仓库完全初始化
        await new Promise(resolve => setTimeout(resolve, 2000));

    } catch (error) {
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            throw new Error('网络连接失败，请检查网络连接');
        }
        throw error;
    }
}

// 验证 YAML 语法 (简单检查)
function validateYAMLSyntax(yamlContent) {
    const lines = yamlContent.split('\n');
    const errors = [];

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const lineNum = i + 1;

        // 检查缩进是否为偶数空格
        const indent = line.match(/^( *)/)[1].length;
        if (indent % 2 !== 0 && line.trim() !== '') {
            errors.push(`Line ${lineNum}: 缩进应该是偶数个空格`);
        }

        // 检查是否有制表符
        if (line.includes('\t')) {
            errors.push(`Line ${lineNum}: 不应该使用制表符，请使用空格`);
        }

        // 检查冒号后是否有空格
        if (line.includes(':') && !line.includes(': ') && !line.endsWith(':') && line.trim() !== '') {
            const colonIndex = line.indexOf(':');
            if (colonIndex < line.length - 1 && line[colonIndex + 1] !== ' ') {
                errors.push(`Line ${lineNum}: 冒号后应该有空格`);
            }
        }
    }

    return errors;
}

// 上传优化的工作流文件
async function uploadOptimizedWorkflowFile() {
    const workflowContent = generateWorkflowContent();

    // 验证 YAML 语法
    const yamlErrors = validateYAMLSyntax(workflowContent);
    if (yamlErrors.length > 0) {
        console.warn('YAML syntax warnings:', yamlErrors);
    }

    console.log('Generated optimized workflow content preview:');
    console.log(workflowContent.substring(0, 500) + '...');

    await retryOperation(async () => {
        await uploadFileToGitHub(
            '.github/workflows/upload-testflight.yml',
            workflowContent,
            'Add optimized GitHub Actions workflow for TestFlight upload'
        );
    });
}

// 上传工作流文件 (保留兼容性)
async function uploadWorkflowFile() {
    return await uploadOptimizedWorkflowFile();
}

// 上传前端文件
async function uploadFrontendFiles() {
    // 上传 README
    const readmeContent = generateReadmeContent();
    await retryOperation(async () => {
        await uploadFileToGitHub('README.md', readmeContent, 'Add README');
    });

    // 上传前端界面 (可选)
    // const htmlContent = document.documentElement.outerHTML;
    // await uploadFileToGitHub('index.html', htmlContent, 'Add frontend interface');
}





// 一键自动上传
async function startAutomatedUpload() {
    const ipaFile = document.getElementById('ipa-file').files[0];
    const releaseNotes = document.getElementById('release-notes').value;
    const appIdentifier = document.getElementById('app-identifier').value;

    if (!ipaFile) {
        alert('请选择 IPA 文件');
        return;
    }

    if (!appIdentifier) {
        alert('请填写 Bundle ID');
        return;
    }

    // 检查是否已配置 GitHub
    const username = document.getElementById('github-username').value;
    const token = document.getElementById('github-token').value;

    if (!username || !token) {
        alert('请先配置 GitHub 账号信息');
        return;
    }

    githubToken = token;
    repoOwner = username;
    repoName = document.getElementById('repo-name').value || 'ios-uploader';

    const progressDiv = document.getElementById('automated-upload-progress');
    const uploadBtn = document.getElementById('automated-upload-btn');

    uploadBtn.disabled = true;
    progressDiv.innerHTML = '<div class="progress"><div class="progress-bar"><div class="progress-fill"></div></div><p>开始自动化上传流程...</p></div>';

    try {
        // 步骤 1: 确保 repository 存在
        if (!repoCreated) {
            progressDiv.innerHTML = '<div class="progress"><div class="progress-bar"><div class="progress-fill"></div></div><p>📁 创建 GitHub repository...</p></div>';
            await setupGitHubRepo();
        }

        // 步骤 2: 生成包含认证信息的工作流
        progressDiv.innerHTML = '<div class="progress"><div class="progress-bar"><div class="progress-fill"></div></div><p>🔧 生成包含认证信息的工作流...</p></div>';

        // 验证认证信息
        const authMethod = document.querySelector('input[name="auth-method"]:checked').value;
        if (authMethod === 'api_key') {
            const apiKeyId = document.getElementById('api-key-id').value;
            const issuerId = document.getElementById('issuer-id').value;
            const apiKeyContent = document.getElementById('api-key-content').value;

            if (!apiKeyId || !issuerId || !apiKeyContent) {
                throw new Error('请填写完整的 API Key 信息');
            }
        } else {
            const appleId = document.getElementById('apple-id').value;
            const appPassword = document.getElementById('app-password').value;

            if (!appleId || !appPassword) {
                throw new Error('请填写完整的 Apple ID 信息');
            }
        }

        // 重新生成优化的工作流文件
        await retryOperation(async () => {
            await uploadOptimizedWorkflowFile();
        });

        // 步骤 3: 上传 IPA 文件
        progressDiv.innerHTML = '<div class="progress"><div class="progress-bar"><div class="progress-fill"></div></div><p>📱 上传 IPA 文件...</p></div>';
        await uploadFileToGitHub(ipaFile.name, ipaFile, `Upload ${ipaFile.name}`, true);

        // 步骤 4: 触发 GitHub Actions (只触发一次)
        progressDiv.innerHTML = '<div class="progress"><div class="progress-bar"><div class="progress-fill"></div></div><p>🚀 触发 GitHub Actions...</p></div>';

        // 检查是否已经有正在运行的工作流
        const runningWorkflows = await checkRunningWorkflows();
        if (runningWorkflows.length > 0) {
            console.log('Found running workflows, skipping trigger');
            progressDiv.innerHTML = `
                <div class="alert alert-info">
                    <strong>ℹ️ 检测到正在运行的工作流</strong>
                    <br>已有 ${runningWorkflows.length} 个工作流正在运行，跳过重复触发
                    <br><a href="https://github.com/${repoOwner}/${repoName}/actions" target="_blank" class="github-link">
                        <i class="fab fa-github"></i> 查看运行中的工作流
                    </a>
                </div>
            `;
        } else {
            await triggerGitHubAction(ipaFile.name, releaseNotes);
        }

        // 完成
        progressDiv.innerHTML = `
            <div class="alert alert-success">
                <strong>🎉 自动上传流程启动成功！</strong>
                <br>✅ Repository 创建完成
                <br>✅ 认证信息已嵌入工作流
                <br>✅ IPA 文件上传完成
                <br>✅ GitHub Actions 已自动触发
                <br><br>
                <a href="https://github.com/${repoOwner}/${repoName}/actions" target="_blank" class="github-link">
                    <i class="fab fa-github"></i> 查看 GitHub Actions 上传进度
                </a>
                <br><small>上传通常需要 5-10 分钟完成</small>
            </div>
        `;

    } catch (error) {
        console.error('Automated upload failed:', error);
        progressDiv.innerHTML = `
            <div class="alert alert-danger">
                <strong>❌ 自动上传失败：</strong> ${error.message}
            </div>
        `;
    } finally {
        uploadBtn.disabled = false;
    }
}











// 触发上传 (保留原有功能)
async function triggerUpload() {
    const ipaFile = document.getElementById('ipa-file').files[0];
    const releaseNotes = document.getElementById('release-notes').value;
    
    if (!ipaFile) {
        alert('请选择 IPA 文件');
        return;
    }

    if (!repoOwner || !repoName) {
        alert('请输入有效的 GitHub Repository URL');
        return;
    }

    // 显示进度
    document.getElementById('upload-progress').style.display = 'block';
    document.getElementById('upload-btn').disabled = true;

    try {
        // 步骤 1: 上传 IPA 文件到 GitHub
        await uploadFileToGitHub(ipaFile);
        
        // 步骤 2: 触发 GitHub Actions
        await triggerGitHubAction(ipaFile.name, releaseNotes);
        
        // 显示成功信息
        showUploadResult(true, 'Upload triggered successfully! Check GitHub Actions for progress.');
        
    } catch (error) {
        console.error('Upload failed:', error);
        showUploadResult(false, 'Upload failed: ' + error.message);
    } finally {
        document.getElementById('upload-progress').style.display = 'none';
        document.getElementById('upload-btn').disabled = false;
    }
}

// 安全的 Base64 编码函数
function safeBase64Encode(str) {
    try {
        // 对于包含 Unicode 字符的字符串，先转换为 UTF-8 字节
        const utf8Bytes = new TextEncoder().encode(str);
        let binary = '';
        utf8Bytes.forEach(byte => {
            binary += String.fromCharCode(byte);
        });
        return btoa(binary);
    } catch (error) {
        console.error('Base64 encoding failed:', error);
        throw new Error('Failed to encode content to Base64');
    }
}

// 重试机制
async function retryOperation(operation, maxRetries = 3, delay = 1000) {
    for (let i = 0; i < maxRetries; i++) {
        try {
            return await operation();
        } catch (error) {
            console.log(`Operation failed (attempt ${i + 1}/${maxRetries}):`, error.message);

            if (i === maxRetries - 1) {
                throw error; // 最后一次尝试失败，抛出错误
            }

            // 等待一段时间后重试
            await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
        }
    }
}

// 获取文件的 SHA (如果文件存在)
async function getFileSHA(fileName) {
    try {
        const response = await fetch(`https://api.github.com/repos/${repoOwner}/${repoName}/contents/${fileName}`, {
            headers: {
                'Authorization': `token ${githubToken}`,
                'Accept': 'application/vnd.github.v3+json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            return data.sha;
        }
        return null; // 文件不存在
    } catch (error) {
        return null; // 文件不存在或其他错误
    }
}

// 上传文件到 GitHub (支持文本和二进制文件，自动处理文件更新)
async function uploadFileToGitHub(fileName, fileContent, commitMessage, isBinary = false) {
    let content;

    if (isBinary && fileContent instanceof File) {
        // 处理二进制文件 (如 IPA)
        content = await new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    // 使用 ArrayBuffer 读取，然后转换为 Base64
                    const arrayBuffer = e.target.result;
                    const uint8Array = new Uint8Array(arrayBuffer);
                    let binary = '';
                    uint8Array.forEach(byte => {
                        binary += String.fromCharCode(byte);
                    });
                    resolve(btoa(binary));
                } catch (error) {
                    reject(new Error('Failed to encode binary file'));
                }
            };
            reader.onerror = () => reject(new Error('Failed to read file'));
            reader.readAsArrayBuffer(fileContent); // 使用 ArrayBuffer 而不是 BinaryString
        });
    } else if (typeof fileContent === 'string') {
        // 处理文本文件，使用安全的 Base64 编码
        content = safeBase64Encode(fileContent);
    } else {
        throw new Error('Unsupported file content type');
    }

    // 检查文件是否已存在，获取 SHA
    const existingSHA = await getFileSHA(fileName);

    const requestBody = {
        message: commitMessage,
        content: content,
        branch: 'main'
    };

    // 如果文件已存在，添加 SHA
    if (existingSHA) {
        requestBody.sha = existingSHA;
        console.log(`File ${fileName} exists, updating with SHA: ${existingSHA}`);
    } else {
        console.log(`Creating new file: ${fileName}`);
    }

    const response = await fetch(`https://api.github.com/repos/${repoOwner}/${repoName}/contents/${fileName}`, {
        method: 'PUT',
        headers: {
            'Authorization': `token ${githubToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/vnd.github.v3+json'
        },
        body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
        const error = await response.json();
        console.error(`Upload failed for ${fileName}:`, error);
        throw new Error(`Failed to upload ${fileName}: ${error.message || response.statusText}`);
    }

    const result = await response.json();
    console.log(`Successfully uploaded ${fileName}`);
    return result;
}

// 检查正在运行的工作流
async function checkRunningWorkflows() {
    try {
        const response = await fetch(`https://api.github.com/repos/${repoOwner}/${repoName}/actions/runs?status=in_progress`, {
            headers: {
                'Authorization': `token ${githubToken}`,
                'Accept': 'application/vnd.github.v3+json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            return data.workflow_runs || [];
        }
        return [];
    } catch (error) {
        console.warn('Failed to check running workflows:', error);
        return [];
    }
}

// 触发 GitHub Actions
async function triggerGitHubAction(fileName, releaseNotes) {
    console.log(`Triggering GitHub Actions for file: ${fileName}`);

    try {
        const response = await fetch(`https://api.github.com/repos/${repoOwner}/${repoName}/actions/workflows/upload-testflight.yml/dispatches`, {
            method: 'POST',
            headers: {
                'Authorization': `token ${githubToken}`,
                'Content-Type': 'application/json',
                'Accept': 'application/vnd.github.v3+json'
            },
            body: JSON.stringify({
                ref: 'main',
                inputs: {
                    ipa_file: fileName,
                    release_notes: releaseNotes || '新版本发布'
                }
            })
        });

        if (!response.ok) {
            const error = await response.json();
            console.error('GitHub Actions trigger failed:', error);

            if (response.status === 404) {
                throw new Error('工作流文件未找到，请确认 .github/workflows/upload-testflight.yml 文件已正确上传');
            }

            if (response.status === 422) {
                throw new Error('工作流触发失败，可能是 YAML 语法错误或分支不存在');
            }

            throw new Error(`Failed to trigger workflow: ${error.message || response.statusText}`);
        }

        console.log('GitHub Actions triggered successfully');

        // 等待一下让 Actions 开始运行
        await new Promise(resolve => setTimeout(resolve, 2000));

    } catch (error) {
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            throw new Error('网络连接失败，请检查网络连接');
        }
        throw error;
    }
}

// 显示上传结果
function showUploadResult(success, message) {
    const resultDiv = document.getElementById('upload-result');
    resultDiv.innerHTML = `
        <div class="alert ${success ? 'alert-success' : 'alert-danger'}">
            <strong>${success ? '成功!' : '失败!'}</strong> ${message}
            ${success ? `<br><a href="https://github.com/${repoOwner}/${repoName}/actions" target="_blank" class="github-link">
                <i class="fab fa-github"></i> 查看 GitHub Actions 进度
            </a>` : ''}
        </div>
    `;
}

// 安全的 YAML 多行字符串生成
function generateYAMLMultilineString(content, baseIndent = 8) {
    const lines = content.split('\n');
    const indentStr = ' '.repeat(baseIndent);
    return lines.map(line => indentStr + line).join('\n');
}



// 生成正确的工作流内容 (使用官方推荐的 altool 方法)
function generateWorkflowContent() {
    const authMethod = document.querySelector('input[name="auth-method"]:checked').value;

    if (authMethod === 'api_key') {
        const apiKeyId = document.getElementById('api-key-id').value;
        const issuerId = document.getElementById('issuer-id').value;
        const apiKeyContent = document.getElementById('api-key-content').value;

        return `name: Upload to TestFlight

# 使用官方推荐的 altool 方法
# 生成时间: ${new Date().toISOString()}

on:
  workflow_dispatch:
    inputs:
      ipa_file:
        description: 'IPA file name in repository'
        required: true
        default: 'app.ipa'
      release_notes:
        description: 'Release notes'
        required: false
        default: '新版本发布'

jobs:
  upload:
    runs-on: macos-latest
    timeout-minutes: 20
    env:
      API_KEY_ID: "${apiKeyId}"
      ISSUER_ID: "${issuerId}"
      API_KEY_CONTENT: |
${generateYAMLMultilineString(apiKeyContent)}

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Verify IPA file
      run: |
        if [ ! -f "\${{ github.event.inputs.ipa_file }}" ]; then
          echo "❌ IPA file not found: \${{ github.event.inputs.ipa_file }}"
          exit 1
        fi
        echo "✅ IPA file found: \${{ github.event.inputs.ipa_file }}"
        ls -la "\${{ github.event.inputs.ipa_file }}"

    - name: Create API Key file
      run: |
        mkdir -p ~/private_keys
        echo "$API_KEY_CONTENT" > ~/private_keys/AuthKey_$API_KEY_ID.p8
        chmod 600 ~/private_keys/AuthKey_$API_KEY_ID.p8

    - name: Validate App
      id: validate
      continue-on-error: true
      run: |
        echo "🔍 Validating app before upload..."

        # 运行验证并捕获输出
        if xcrun altool --validate-app \\
          -f "\${{ github.event.inputs.ipa_file }}" \\
          -t ios \\
          --apiKey "$API_KEY_ID" \\
          --apiIssuer "$ISSUER_ID" \\
          --verbose 2>&1 | tee validation_output.log; then
          echo "✅ Validation passed"
          echo "validation_result=success" >> $GITHUB_OUTPUT
        else
          echo "❌ Validation failed"
          echo "validation_result=failed" >> $GITHUB_OUTPUT

          # 检查具体的错误类型
          if grep -q "bundle version must be higher" validation_output.log; then
            echo "error_type=version_exists" >> $GITHUB_OUTPUT
            echo "🔍 Error: Bundle version already exists"
          elif grep -q "ENTITY_ERROR.ATTRIBUTE.INVALID.DUPLICATE" validation_output.log; then
            echo "error_type=duplicate_version" >> $GITHUB_OUTPUT
            echo "🔍 Error: Duplicate version detected"
          elif grep -q "authentication" validation_output.log; then
            echo "error_type=auth_failed" >> $GITHUB_OUTPUT
            echo "🔍 Error: Authentication failed"
          elif grep -q "Invalid bundle" validation_output.log; then
            echo "error_type=invalid_bundle" >> $GITHUB_OUTPUT
            echo "🔍 Error: Invalid bundle"
          else
            echo "error_type=unknown" >> $GITHUB_OUTPUT
            echo "🔍 Error: Unknown validation error"
          fi

          # 显示错误详情
          echo "📋 Validation error details:"
          cat validation_output.log
        fi

    - name: Check Validation Result
      id: check_validation
      run: |
        if [ "\${{ steps.validate.outputs.validation_result }}" = "failed" ]; then
          ERROR_TYPE="\${{ steps.validate.outputs.error_type }}"
          echo "🔍 Validation failed with error type: $ERROR_TYPE"

          case "$ERROR_TYPE" in
            "version_exists"|"duplicate_version")
              echo "⚠️ Version already exists in App Store"
              echo "💡 Solution: Increase the bundle version (CFBundleVersion) in your app"
              echo "should_skip_upload=true" >> $GITHUB_OUTPUT
              echo "skip_reason=Version already exists" >> $GITHUB_OUTPUT
              ;;
            "auth_failed")
              echo "❌ Authentication failed"
              echo "💡 Solution: Check your API Key ID, Issuer ID, and private key"
              echo "should_skip_upload=true" >> $GITHUB_OUTPUT
              echo "skip_reason=Authentication failed" >> $GITHUB_OUTPUT
              ;;
            "invalid_bundle")
              echo "❌ Invalid bundle"
              echo "💡 Solution: Check your IPA file integrity and signing"
              echo "should_skip_upload=true" >> $GITHUB_OUTPUT
              echo "skip_reason=Invalid bundle" >> $GITHUB_OUTPUT
              ;;
            *)
              echo "❌ Unknown validation error"
              echo "💡 Will attempt upload anyway"
              echo "should_skip_upload=false" >> $GITHUB_OUTPUT
              ;;
          esac
        else
          echo "✅ Validation passed, proceeding with upload"
          echo "should_skip_upload=false" >> $GITHUB_OUTPUT
        fi

    - name: Extract App Info
      id: app_info
      if: steps.check_validation.outputs.should_skip_upload != 'true'
      run: |
        echo "📋 Extracting app information from IPA..."

        # 解压 IPA 文件
        unzip -q "\${{ github.event.inputs.ipa_file }}" -d temp_ipa

        # 找到 Info.plist 文件
        INFO_PLIST=\$(find temp_ipa -name "Info.plist" | head -1)

        if [ -z "$INFO_PLIST" ]; then
          echo "❌ Info.plist not found in IPA"
          exit 1
        fi

        # 提取信息
        BUNDLE_ID=\$(plutil -extract CFBundleIdentifier raw "$INFO_PLIST")
        VERSION=\$(plutil -extract CFBundleShortVersionString raw "$INFO_PLIST")
        BUILD=\$(plutil -extract CFBundleVersion raw "$INFO_PLIST")

        echo "Bundle ID: $BUNDLE_ID"
        echo "Version: $VERSION"
        echo "Build: $BUILD"

        # 输出到 GitHub Actions
        echo "bundle_id=$BUNDLE_ID" >> $GITHUB_OUTPUT
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "build=$BUILD" >> $GITHUB_OUTPUT

        # 清理临时文件
        rm -rf temp_ipa

    - name: Upload to TestFlight (Smart Error Handling)
      if: steps.check_validation.outputs.should_skip_upload != 'true'
      run: |
        echo "🚀 Uploading to TestFlight with built-in diagnostics..."

        # 预上传诊断
        echo "📋 Upload diagnostics:"
        echo "File: \${{ github.event.inputs.ipa_file }}"
        echo "Size: \$(ls -lh '\${{ github.event.inputs.ipa_file }}' | awk '{print \$5}')"
        echo "Bundle ID: \${{ steps.app_info.outputs.bundle_id }}"
        echo "Version: \${{ steps.app_info.outputs.version }}"
        echo "Build: \${{ steps.app_info.outputs.build }}"
        echo "Auth: API Key (\$API_KEY_ID)"
        echo ""

        # 尝试上传并捕获详细输出
        echo "📱 Using altool --upload-package (recommended)..."
        if xcrun altool --upload-package \\
          "\${{ github.event.inputs.ipa_file }}" \\
          -t ios \\
          --apiKey "$API_KEY_ID" \\
          --apiIssuer "$ISSUER_ID" \\
          --bundle-id "\${{ steps.app_info.outputs.bundle_id }}" \\
          --bundle-short-version-string "\${{ steps.app_info.outputs.version }}" \\
          --bundle-version "\${{ steps.app_info.outputs.build }}" \\
          --verbose 2>&1 | tee upload_output.log; then
          echo "✅ Upload succeeded!"
          echo "🎉 Your app is now processing in App Store Connect"
          exit 0
        fi

        echo "❌ Upload failed, analyzing error..."

        # 智能错误分析 - 不重试明确的错误
        if grep -q "bundle version must be higher" upload_output.log; then
          echo "🔍 错误类型: 版本重复"
          echo "💡 解决方案: 增加应用的 bundle version (当前: \${{ steps.app_info.outputs.build }})"
          echo "📋 详细错误:"
          cat upload_output.log
          exit 1

        elif grep -q "ENTITY_ERROR.ATTRIBUTE.INVALID.DUPLICATE" upload_output.log; then
          echo "🔍 错误类型: 重复版本"
          echo "💡 解决方案: 修改版本号后重新构建"
          echo "📋 详细错误:"
          cat upload_output.log
          exit 1

        elif grep -q "Invalid bundle" upload_output.log; then
          echo "🔍 错误类型: 无效的 IPA 包"
          echo "💡 解决方案: 检查 IPA 文件完整性和签名"
          echo "📋 详细错误:"
          cat upload_output.log
          exit 1

        elif grep -q "does not contain a valid signature" upload_output.log; then
          echo "🔍 错误类型: 签名问题"
          echo "💡 解决方案: 重新签名应用"
          echo "📋 详细错误:"
          cat upload_output.log
          exit 1

        elif grep -q "API key" upload_output.log || grep -q "authentication" upload_output.log; then
          echo "🔍 错误类型: API Key 认证问题"
          echo "💡 解决方案: 检查 API Key ID、Issuer ID 和私钥内容"
          echo "📋 详细错误:"
          cat upload_output.log
          exit 1

        else
          echo "🔍 错误类型: 未知错误"
          echo "📋 完整错误信息:"
          cat upload_output.log
          echo "💡 建议: 检查上述错误信息，如有需要可重新运行"
          exit 1
        fi



    - name: Clean up
      if: always()
      run: |
        rm -rf ~/private_keys
        echo "🧹 Cleanup completed"

    - name: Upload result
      if: always()
      run: |
        if [ "\${{ steps.check_validation.outputs.should_skip_upload }}" = "true" ]; then
          echo "⏭️ Upload skipped due to validation error"
          echo "📋 Reason: \${{ steps.check_validation.outputs.skip_reason }}"
          echo ""
          echo "💡 Next steps:"
          case "\${{ steps.validate.outputs.error_type }}" in
            "version_exists"|"duplicate_version")
              echo "1. Increase your app's bundle version (CFBundleVersion)"
              echo "2. Build a new IPA with the higher version"
              echo "3. Upload the new IPA file"
              ;;
            "auth_failed")
              echo "1. Verify your API Key ID is correct (10 characters)"
              echo "2. Check your Issuer ID format (UUID)"
              echo "3. Ensure your private key is complete and valid"
              echo "4. Confirm your API Key has App Manager permissions"
              ;;
            "invalid_bundle")
              echo "1. Check your IPA file is not corrupted"
              echo "2. Verify your app is properly signed"
              echo "3. Ensure all required metadata is present"
              ;;
          esac
        elif [ "\${{ job.status }}" == "success" ]; then
          echo "🎉 Upload to TestFlight completed successfully!"
          echo "Your app should appear in TestFlight within a few minutes."
          echo ""
          echo "📱 Next steps:"
          echo "1. Check TestFlight for your new build"
          echo "2. Add release notes if needed"
          echo "3. Distribute to internal testers"
        else
          echo "💥 Upload failed. Please check the logs above for details."
          echo ""
          echo "🔍 Common solutions:"
          echo "1. Check your API Key credentials"
          echo "2. Verify your IPA file integrity"
          echo "3. Ensure your bundle version is higher than existing versions"
        fi`;

    } else {
        const appleId = document.getElementById('apple-id').value;
        const appPassword = document.getElementById('app-password').value;

        return `name: Upload to TestFlight

# 自动生成的工作流 - 直接嵌入 Apple ID 认证信息
# 生成时间: ${new Date().toISOString()}

on:
  workflow_dispatch:
    inputs:
      ipa_file:
        description: 'IPA file name in repository'
        required: true
        default: 'app.ipa'
      release_notes:
        description: 'Release notes'
        required: false
        default: '新版本发布'

jobs:
  upload:
    runs-on: macos-latest
    timeout-minutes: 30
    env:
      APPLE_ID: "${appleId}"
      APP_PASSWORD: "${appPassword}"

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Xcode
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: latest-stable

    - name: Verify IPA file
      run: |
        if [ ! -f "\${{ github.event.inputs.ipa_file }}" ]; then
          echo "❌ IPA file not found: \${{ github.event.inputs.ipa_file }}"
          exit 1
        fi
        echo "✅ IPA file found: \${{ github.event.inputs.ipa_file }}"
        ls -la "\${{ github.event.inputs.ipa_file }}"

    - name: Validate App Specific Password Format
      run: |
        echo "🔍 Validating App Specific Password format..."
        echo "Apple ID: $APPLE_ID"
        echo "Password length: \${#APP_PASSWORD}"

        # 检查专属密码格式
        if [[ ! "$APP_PASSWORD" =~ ^[a-z]{4}-[a-z]{4}-[a-z]{4}-[a-z]{4}$ ]]; then
          echo "❌ 专属密码格式错误！"
          echo "正确格式: xxxx-xxxx-xxxx-xxxx (16个小写字母)"
          echo "当前格式: $APP_PASSWORD"
          echo ""
          echo "💡 解决方案:"
          echo "1. 访问 https://appleid.apple.com/account/manage"
          echo "2. 生成新的专属密码"
          echo "3. 确保格式为 xxxx-xxxx-xxxx-xxxx"
          exit 1
        fi

        echo "✅ 专属密码格式正确"

    - name: Skip Validation (Following Fastlane Best Practice)
      run: |
        echo "ℹ️ 跳过验证步骤 (遵循 Fastlane 最佳实践)"
        echo ""
        echo "根据 Fastlane 官方文档:"
        echo "专属密码只能用于上传二进制文件，不能用于验证或元数据操作"
        echo "因此我们跳过验证步骤，直接进行上传"
        echo ""
        echo "参考: https://docs.fastlane.tools/getting-started/ios/authentication/"

    - name: Upload using Apple ID (Smart Error Handling)
      run: |
        echo "🍎 Uploading using Apple ID with smart error detection..."

        # 尝试上传并捕获详细输出
        echo "📱 Using altool with Apple ID..."
        if xcrun altool --upload-app \\
          -f "\${{ github.event.inputs.ipa_file }}" \\
          -t ios \\
          -u "$APPLE_ID" \\
          -p "$APP_PASSWORD" \\
          --verbose 2>&1 | tee upload_output.log; then
          echo "✅ Upload succeeded!"
          exit 0
        fi

        echo "❌ Upload failed, analyzing error..."

        # 分析错误类型，决定是否重试
        if grep -q "bundle version must be higher" upload_output.log; then
          echo "🔍 错误类型: 版本重复"
          echo "💡 解决方案: 增加应用的 bundle version"
          echo "📋 详细错误:"
          cat upload_output.log
          exit 1  # 不重试，直接失败

        elif grep -q "ENTITY_ERROR.ATTRIBUTE.INVALID.DUPLICATE" upload_output.log; then
          echo "🔍 错误类型: 重复版本"
          echo "💡 解决方案: 修改版本号后重新构建"
          echo "📋 详细错误:"
          cat upload_output.log
          exit 1  # 不重试，直接失败

        elif grep -q "Invalid bundle" upload_output.log; then
          echo "🔍 错误类型: 无效的 IPA 包"
          echo "💡 解决方案: 检查 IPA 文件完整性和签名"
          echo "📋 详细错误:"
          cat upload_output.log
          exit 1  # 不重试，直接失败

        elif grep -q "does not contain a valid signature" upload_output.log; then
          echo "🔍 错误类型: 签名问题"
          echo "� 解决方案: 重新签名应用"
          echo "📋 详细错误:"
          cat upload_output.log
          exit 1  # 不重试，直接失败

        elif grep -q "bad status code" upload_output.log || grep -q "Status code: 0" upload_output.log; then
          echo "🔍 错误类型: 网络或服务器临时问题"
          echo "💡 可以重试: 尝试 Transporter 方法"

          echo "🚛 Trying Transporter as fallback..."
          if xcrun iTMSTransporter -m upload \\
            -f "\${{ github.event.inputs.ipa_file }}" \\
            -u "$APPLE_ID" \\
            -p "$APP_PASSWORD" \\
            -v eXtreme 2>&1 | tee transporter_output.log; then
            echo "✅ Transporter upload succeeded!"
            exit 0
          else
            echo "❌ Transporter also failed"
            echo "📋 Transporter 错误:"
            cat transporter_output.log
            exit 1
          fi

        else
          echo "🔍 错误类型: 未知错误"
          echo "📋 完整错误信息:"
          cat upload_output.log
          exit 1  # 不重试，直接失败
        fi

    - name: Multi-Method Fallback
      if: failure()
      run: |
        echo "⚠️ Primary method failed, trying comprehensive fallback strategy..."

        # 方法 1: Transporter 重试
        echo "🔄 Retrying Transporter..."
        if xcrun iTMSTransporter -m upload \\
          -f "\${{ github.event.inputs.ipa_file }}" \\
          -u "$APPLE_ID" \\
          -p "$APP_PASSWORD" \\
          -v eXtreme 2>/dev/null; then
          echo "✅ Transporter retry succeeded"
          exit 0
        fi

        echo "❌ All upload methods failed"
        exit 1

    - name: Upload result
      if: always()
      run: |
        if [ "\${{ job.status }}" == "success" ]; then
          echo "🎉 Upload to TestFlight completed successfully!"
          echo "Your app should appear in TestFlight within a few minutes."
        else
          echo "💥 Upload failed. Please check the logs above for details."
        fi`;
    }
}

// 生成 README 内容
function generateReadmeContent() {
    return `# 🚀 iOS App Uploader

自动生成的 iOS 应用上传系统，使用 GitHub Actions 实现零成本上传到 TestFlight。

## ✨ 特性

- 🆓 **完全免费** - 基于 GitHub Actions 免费额度
- 🔒 **安全可靠** - 使用 GitHub Secrets 管理敏感信息
- 🔄 **多重备选** - 支持多种苹果上传工具
- 📱 **一键上传** - 简单易用的自动化流程

## 🚀 使用方法

1. 将 IPA 文件上传到 repository 根目录
2. 进入 Actions 标签页
3. 选择 "Upload to TestFlight" 工作流
4. 点击 "Run workflow" 并填写参数
5. 等待上传完成

## 🔧 支持的上传工具

- **Transporter** (最稳定，苹果推荐)
- **altool --upload-package** (新命令)
- **altool --upload-app** (旧命令，已弃用)
- **Fastlane** (综合方案)

## 📊 GitHub Actions 使用情况

- 每次上传约消耗 5-10 分钟
- 免费账户每月 2000 分钟额度
- 支持约 200-400 次上传/月

---

*自动生成于 ${new Date().toLocaleString()}*`;
}

// 工具函数：复制到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        alert('已复制到剪贴板');
    });
}
