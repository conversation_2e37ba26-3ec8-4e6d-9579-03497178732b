<?php

require_once 'vendor/autoload.php';

use App\Utils\IpaParser;

echo "=== IPA解析器测试 ===\n\n";

// 测试文件路径
$testFiles = [
    'storage/uploads/ipa/test.ipa',
    'storage/uploads/upload_tmp/test.ipa',
    '/tmp/test.ipa',
    '/data/storage/uploads/ipa/689bfc23fa45c47ad303d4c2_1755053311.ipa'
];

foreach ($testFiles as $testFile) {
    if (file_exists($testFile)) {
        echo "找到测试文件: $testFile\n";

        try {
            echo "1. 验证IPA文件...\n";
            $isValid = IpaParser::isValidIpa($testFile);
            echo "   有效性: " . ($isValid ? "✅ 有效" : "❌ 无效") . "\n";

            if ($isValid) {
                echo "2. 获取基本信息...\n";
                $basicInfo = IpaParser::getIpaBasicInfo($testFile);
                echo "   文件大小: " . number_format($basicInfo['file_size']) . " 字节\n";
                echo "   应用名称: " . $basicInfo['app_name'] . "\n";
                echo "   是否有效: " . ($basicInfo['is_valid'] ? "是" : "否") . "\n";

                echo "3. 完整解析...\n";
                $fullInfo = IpaParser::parseInfoPlist($testFile);
                echo "   Bundle ID: " . $fullInfo['bundle_id'] . "\n";
                echo "   应用名称: " . $fullInfo['app_name'] . "\n";
                echo "   版本号: " . $fullInfo['version'] . "\n";
                echo "   构建号: " . $fullInfo['build'] . "\n";
                echo "   最低iOS版本: " . $fullInfo['minimum_os_version'] . "\n";
                echo "   支持设备: " . json_encode($fullInfo['supported_devices']) . "\n";
            }
        } catch (Exception $e) {
            echo "❌ 解析失败: " . $e->getMessage() . "\n";
        }

        echo "\n" . str_repeat("-", 50) . "\n\n";
        break;
    }
}

if (!isset($testFile) || !file_exists($testFile)) {
    echo "❌ 没有找到测试IPA文件\n";
    echo "请将IPA文件放在以下位置之一:\n";
    foreach ($testFiles as $path) {
        echo "  - $path\n";
    }
}

echo "=== 测试完成 ===\n";
