<?php

require_once __DIR__ . '/../vendor/autoload.php';

use App\Config\AppConfig;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Matcher\UrlMatcher;
use Symfony\Component\Routing\RequestContext;
use Symfony\Component\Routing\RouteCollection;

// 加载配置
$config = AppConfig::getInstance();

// 设置错误处理
if ($config->isDevelopment()) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// 设置CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // 创建请求对象
    $request = Request::createFromGlobals();

    // 加载路由
    $routes = require __DIR__ . '/../src/Routes/api.php';

    // 创建路由匹配器
    $context = new RequestContext();
    $context->fromRequest($request);
    $matcher = new UrlMatcher($routes, $context);

    // 匹配路由
    $parameters = $matcher->match($request->getPathInfo());

    // 获取控制器和方法
    $controllerClass = $parameters['_controller'][0];
    $method = $parameters['_controller'][1];

    // 创建控制器实例
    $controller = new $controllerClass();

    // 将路由参数设置到request的attributes中
    foreach ($parameters as $key => $value) {
        if ($key !== '_controller' && $key !== '_route') {
            $request->attributes->set($key, $value);
        }
    }

    // 调用控制器方法
    $response = call_user_func([$controller, $method], $request);

    // 发送响应
    $response->send();
} catch (\Symfony\Component\Routing\Exception\ResourceNotFoundException $e) {
    // 路由未找到
    $response = new Response(json_encode([
        'success' => false,
        'error' => '接口不存在'
    ]), 404);
    $response->send();
} catch (\Exception $e) {
    // 其他错误
    $errorMessage = $config->isDevelopment() ? $e->getMessage() : '服务器内部错误';

    $response = new Response(json_encode([
        'success' => false,
        'error' => $errorMessage
    ]), 500);
    $response->send();
}
